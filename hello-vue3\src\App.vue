<template>
   <div id="app">
    <Suspense>
      <router-view />

      <template #fallback>
        <div class="app-loading">
          <van-loading type="spinner" size="24px">加载中...</van-loading>
        </div>
      </template>
    </Suspense>

    <!-- 全局加载组件 -->
    <GlobalLoading />
  </div>
</template>

<script lang="ts" setup name="App">
import GlobalLoading from '@/components/Global/GlobalLoading.vue';
</script>
<style scoped>
.app-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #f7f8fa;
}
</style>