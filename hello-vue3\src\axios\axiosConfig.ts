// 引入axios库，用于发送HTTP请求
import axios from 'axios'
import { useLoading, useError, useNetwork } from '@/composables/useGlobal'

// 创建axios实例，配置基础选项
const http = axios.create({
  timeout: 10000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json'
  }
});

// 获取全局状态管理函数
const { setGlobalLoading } = useLoading();
const { handleError, handleApiError } = useError();
const { checkNetworkStatus } = useNetwork();

// 当前请求数量（用于管理全局加载状态）
let requestCount = 0;

// 请求拦截器：在请求发送前执行
http.interceptors.request.use(
  (config) => {
    // 检查网络状态
    if (!checkNetworkStatus()) {
      return Promise.reject(new Error('网络连接不可用'));
    }

    // 增加请求计数
    requestCount++;
    if (requestCount === 1) {
      setGlobalLoading(true);
    }

    // 添加认证头
    const token = window.sessionStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // 添加时间戳防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      };
    }

    return config;
  },
  (error) => {
    // 减少请求计数
    requestCount--;
    if (requestCount === 0) {
      setGlobalLoading(false);
    }

    handleError(error, {
      customMessage: '请求配置错误'
    });

    return Promise.reject(error);
  }
);

// 响应拦截器：在收到响应后执行
http.interceptors.response.use(
  (response) => {
    // 减少请求计数
    requestCount--;
    if (requestCount === 0) {
      setGlobalLoading(false);
    }

    // 统一处理API响应
    const { data } = response;

    // 检查API响应状态
    if (!handleApiError(data)) {
      return Promise.reject(new Error(data?.message || 'API响应错误'));
    }

    return response;
  },
  (error) => {
    // 减少请求计数
    requestCount--;
    if (requestCount === 0) {
      setGlobalLoading(false);
    }

    // 处理不同类型的错误
    let errorMessage = '请求失败';

    if (error.code === 'ECONNABORTED') {
      errorMessage = '请求超时，请稍后重试';
    } else if (error.response) {
      // 服务器响应错误
      const { status, data } = error.response;

      switch (status) {
        case 401:
          errorMessage = '登录已过期，请重新登录';
          // 清除token并跳转到登录页
          window.sessionStorage.removeItem('token');
          window.location.href = '/login';
          break;
        case 403:
          errorMessage = '没有权限访问';
          break;
        case 404:
          errorMessage = '请求的资源不存在';
          break;
        case 500:
          errorMessage = '服务器内部错误';
          break;
        default:
          errorMessage = data?.message || `请求失败 (${status})`;
      }
    } else if (error.request) {
      // 网络错误
      errorMessage = '网络连接失败，请检查网络';
    }

    handleError(error, {
      customMessage: errorMessage
    });

    return Promise.reject(error);
  }
);

// 导出配置好的axios实例供项目使用
export default http;