<template>
  <div class="address-list-page">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="收货地址"
      left-text="返回"
      left-arrow
      @click-left="goBack"
    >
      <template #right>
        <van-icon name="plus" @click="addAddress" />
      </template>
    </van-nav-bar>

    <!-- 地址列表 -->
    <div class="address-list">
      <!-- 空状态 -->
      <div v-if="addresses.length === 0" class="empty-state">
        <van-empty 
          image="search" 
          description="暂无收货地址"
        >
          <van-button type="primary" @click="addAddress">
            添加地址
          </van-button>
        </van-empty>
      </div>

      <!-- 地址项 -->
      <div 
        v-for="address in addresses" 
        :key="address.id" 
        class="address-item"
        :class="{ 'is-default': address.isDefault }"
      >
        <!-- 地址信息 -->
        <div class="address-info" @click="selectAddress(address)">
          <div class="address-header">
            <span class="recipient-name">{{ address.name }}</span>
            <span class="recipient-phone">{{ address.phone }}</span>
            <van-tag v-if="address.isDefault" type="danger" size="mini">默认</van-tag>
          </div>
          
          <div class="address-detail">
            {{ formatAddress(address) }}
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="address-actions">
          <van-button 
            size="small" 
            plain
            @click="editAddress(address)"
          >
            编辑
          </van-button>
          
          <van-button 
            v-if="!address.isDefault"
            size="small" 
            type="primary"
            @click="setDefault(address.id)"
          >
            设为默认
          </van-button>
          
          <van-button 
            size="small" 
            type="danger"
            plain
            @click="deleteAddress(address.id)"
          >
            删除
          </van-button>
        </div>
      </div>
    </div>

    <!-- 添加地址按钮 -->
    <div class="add-address-btn">
      <van-button 
        type="primary" 
        block 
        @click="addAddress"
      >
        新增收货地址
      </van-button>
    </div>

    <!-- 地址编辑弹窗 -->
    <van-popup v-model:show="showEditDialog" position="bottom" round>
      <div class="edit-address-dialog">
        <div class="dialog-header">
          <h3>{{ editingAddress.id ? '编辑地址' : '新增地址' }}</h3>
          <van-icon name="cross" @click="closeEditDialog" />
        </div>
        
        <van-form @submit="saveAddress">
          <van-cell-group>
            <van-field
              v-model="editingAddress.name"
              name="name"
              label="收货人"
              placeholder="请输入收货人姓名"
              :rules="[{ required: true, message: '请输入收货人姓名' }]"
            />
            
            <van-field
              v-model="editingAddress.phone"
              name="phone"
              label="手机号"
              placeholder="请输入手机号"
              type="tel"
              :rules="[
                { required: true, message: '请输入手机号' },
                { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' }
              ]"
            />
            
            <van-field
              v-model="regionText"
              name="region"
              label="所在地区"
              placeholder="请选择省市区"
              readonly
              is-link
              @click="showRegionPicker = true"
              :rules="[{ required: true, message: '请选择所在地区' }]"
            />
            
            <van-field
              v-model="editingAddress.detail"
              name="detail"
              label="详细地址"
              placeholder="请输入详细地址"
              type="textarea"
              rows="3"
              :rules="[{ required: true, message: '请输入详细地址' }]"
            />
            
            <van-field
              v-model="editingAddress.postalCode"
              name="postalCode"
              label="邮政编码"
              placeholder="请输入邮政编码（可选）"
            />
            
            <van-field name="isDefault" label="设为默认地址">
              <template #input>
                <van-switch v-model="editingAddress.isDefault" />
              </template>
            </van-field>
          </van-cell-group>
          
          <div class="form-actions">
            <van-button type="primary" native-type="submit" block>
              保存
            </van-button>
          </div>
        </van-form>
      </div>
    </van-popup>

    <!-- 地区选择器 -->
    <van-popup v-model:show="showRegionPicker" position="bottom">
      <van-area
        :area-list="areaList"
        @confirm="onRegionConfirm"
        @cancel="showRegionPicker = false"
      />
    </van-popup>

    <!-- 删除确认弹窗 -->
    <van-dialog
      v-model:show="showDeleteDialog"
      title="删除地址"
      message="确定要删除这个地址吗？"
      show-cancel-button
      @confirm="confirmDelete"
    />
  </div>
</template>

<script lang="ts" setup name="AddressList">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useStore } from '@/store';
import { Toast } from 'vant';
import type { Address } from '@/types';

const router = useRouter();
const store = useStore();

// 响应式数据
const showEditDialog = ref(false);
const showRegionPicker = ref(false);
const showDeleteDialog = ref(false);
const deleteAddressId = ref<number | null>(null);

const editingAddress = ref<Partial<Address>>({
  name: '',
  phone: '',
  province: '',
  city: '',
  district: '',
  detail: '',
  postalCode: '',
  isDefault: false
});

// 地区数据（简化版，实际项目中应该使用完整的地区数据）
const areaList = ref({
  province_list: {
    110000: '北京市',
    120000: '天津市',
    310000: '上海市',
    500000: '重庆市',
    440000: '广东省',
    320000: '江苏省'
  },
  city_list: {
    110100: '北京市',
    120100: '天津市',
    310100: '上海市',
    500100: '重庆市',
    440100: '广州市',
    440300: '深圳市',
    320100: '南京市',
    320200: '无锡市'
  },
  county_list: {
    110101: '东城区',
    110102: '西城区',
    110105: '朝阳区',
    110106: '丰台区',
    440103: '荔湾区',
    440104: '越秀区',
    440305: '南山区',
    440306: '宝安区'
  }
});

// 计算属性
const addresses = computed(() => store.addresses);

const regionText = computed(() => {
  const { province, city, district } = editingAddress.value;
  if (province && city && district) {
    return `${province} ${city} ${district}`;
  }
  return '';
});

// 方法
function goBack() {
  router.go(-1);
}

function selectAddress(address: Address) {
  // 如果是从其他页面跳转过来选择地址，可以通过事件或回调返回选中的地址
  console.log('选中地址:', address);
}

function addAddress() {
  editingAddress.value = {
    name: '',
    phone: '',
    province: '',
    city: '',
    district: '',
    detail: '',
    postalCode: '',
    isDefault: false
  };
  showEditDialog.value = true;
}

function editAddress(address: Address) {
  editingAddress.value = { ...address };
  showEditDialog.value = true;
}

function closeEditDialog() {
  showEditDialog.value = false;
  editingAddress.value = {
    name: '',
    phone: '',
    province: '',
    city: '',
    district: '',
    detail: '',
    postalCode: '',
    isDefault: false
  };
}

function saveAddress() {
  const addressData = editingAddress.value;
  
  if (!addressData.name || !addressData.phone || !addressData.province || !addressData.detail) {
    Toast.fail('请填写完整的地址信息');
    return;
  }

  if (addressData.id) {
    // 编辑地址
    store.updateAddress(addressData as Address);
    Toast.success('地址更新成功');
  } else {
    // 新增地址
    store.addAddress(addressData);
    Toast.success('地址添加成功');
  }
  
  closeEditDialog();
}

function setDefault(addressId: number) {
  store.updateAddress({ id: addressId, isDefault: true });
  Toast.success('已设为默认地址');
}

function deleteAddress(addressId: number) {
  deleteAddressId.value = addressId;
  showDeleteDialog.value = true;
}

function confirmDelete() {
  if (deleteAddressId.value) {
    store.removeAddress(deleteAddressId.value);
    Toast.success('地址删除成功');
    deleteAddressId.value = null;
  }
  showDeleteDialog.value = false;
}

function onRegionConfirm(values: any) {
  const [province, city, district] = values;
  editingAddress.value.province = province.text;
  editingAddress.value.city = city.text;
  editingAddress.value.district = district.text;
  showRegionPicker.value = false;
}

function formatAddress(address: Address): string {
  return `${address.province} ${address.city} ${address.district} ${address.detail}`;
}

// 初始化地址数据
function initAddresses() {
  if (addresses.value.length === 0) {
    // 添加一个默认地址
    store.addAddress({
      name: '张三',
      phone: '13800138000',
      province: '北京市',
      city: '北京市',
      district: '朝阳区',
      detail: '某某街道123号',
      isDefault: true
    });
  }
}

onMounted(() => {
  initAddresses();
});
</script>

<style scoped>
.address-list-page {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 80px;
}

.address-list {
  padding: 0 16px;
}

.empty-state {
  padding: 60px 20px;
}

.address-item {
  background: white;
  border-radius: 8px;
  margin-bottom: 12px;
  padding: 16px;
  border: 2px solid transparent;
  transition: all 0.3s;
}

.address-item.is-default {
  border-color: #ff6b35;
}

.address-info {
  cursor: pointer;
  margin-bottom: 12px;
}

.address-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.recipient-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.recipient-phone {
  font-size: 14px;
  color: #666;
}

.address-detail {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.address-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.add-address-btn {
  position: fixed;
  bottom: 20px;
  left: 16px;
  right: 16px;
}

/* 弹窗样式 */
.edit-address-dialog {
  padding: 20px;
  max-height: 80vh;
  overflow-y: auto;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.dialog-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.form-actions {
  margin-top: 20px;
}
</style>
