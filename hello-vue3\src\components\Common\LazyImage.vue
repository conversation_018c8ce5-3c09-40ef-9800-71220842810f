<template>
  <div 
    ref="imageContainer" 
    class="lazy-image-container"
    :style="{ width: width, height: height }"
  >
    <img
      v-if="shouldLoad"
      :src="imageSrc"
      :alt="alt"
      :class="['lazy-image', { 'loaded': imageLoaded, 'error': imageError }]"
      @load="onImageLoad"
      @error="onImageError"
    />
    
    <!-- 加载占位符 -->
    <div v-if="!imageLoaded && !imageError" class="image-placeholder">
      <van-loading v-if="shouldLoad" type="spinner" size="20px" />
      <van-icon v-else name="photo" size="24px" color="#ddd" />
    </div>
    
    <!-- 错误占位符 -->
    <div v-if="imageError" class="image-error">
      <van-icon name="warning-o" size="24px" color="#ddd" />
      <span class="error-text">加载失败</span>
    </div>
  </div>
</template>

<script lang="ts" setup name="LazyImage">
import { ref, onMounted, onUnmounted, computed } from 'vue';

// Props定义
interface Props {
  src: string;
  alt?: string;
  width?: string;
  height?: string;
  placeholder?: string;
  threshold?: number; // 触发加载的阈值（0-1）
  rootMargin?: string; // 根边距
}

const props = withDefaults(defineProps<Props>(), {
  alt: '',
  width: '100%',
  height: 'auto',
  placeholder: '',
  threshold: 0.1,
  rootMargin: '50px'
});

// 响应式数据
const imageContainer = ref<HTMLElement>();
const shouldLoad = ref(false);
const imageLoaded = ref(false);
const imageError = ref(false);
const observer = ref<IntersectionObserver>();

// 计算属性
const imageSrc = computed(() => {
  if (imageError.value && props.placeholder) {
    return props.placeholder;
  }
  return props.src;
});

// 方法
function onImageLoad() {
  imageLoaded.value = true;
  imageError.value = false;
}

function onImageError() {
  imageError.value = true;
  imageLoaded.value = false;
}

function createObserver() {
  if (!imageContainer.value) return;

  observer.value = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          shouldLoad.value = true;
          // 开始加载后停止观察
          if (observer.value) {
            observer.value.unobserve(entry.target);
          }
        }
      });
    },
    {
      threshold: props.threshold,
      rootMargin: props.rootMargin
    }
  );

  observer.value.observe(imageContainer.value);
}

function destroyObserver() {
  if (observer.value) {
    observer.value.disconnect();
    observer.value = undefined;
  }
}

// 生命周期
onMounted(() => {
  // 检查是否支持 IntersectionObserver
  if ('IntersectionObserver' in window) {
    createObserver();
  } else {
    // 不支持则直接加载
    shouldLoad.value = true;
  }
});

onUnmounted(() => {
  destroyObserver();
});
</script>

<style scoped>
.lazy-image-container {
  position: relative;
  overflow: hidden;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.lazy-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.lazy-image.loaded {
  opacity: 1;
}

.lazy-image.error {
  opacity: 0;
}

.image-placeholder,
.image-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
}

.error-text {
  font-size: 12px;
  margin-top: 4px;
}
</style>
