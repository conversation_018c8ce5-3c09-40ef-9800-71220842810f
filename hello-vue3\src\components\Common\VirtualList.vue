<template>
  <div 
    ref="containerRef" 
    class="virtual-list-container"
    :style="{ height: containerHeight }"
    @scroll="handleScroll"
  >
    <!-- 虚拟滚动区域 -->
    <div 
      class="virtual-list-phantom" 
      :style="{ height: totalHeight + 'px' }"
    ></div>
    
    <!-- 可见项目容器 -->
    <div 
      class="virtual-list-content"
      :style="{ transform: `translateY(${offsetY}px)` }"
    >
      <div
        v-for="item in visibleItems"
        :key="getItemKey(item.data)"
        class="virtual-list-item"
        :style="{ height: itemHeight + 'px' }"
      >
        <slot :item="item.data" :index="item.index"></slot>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup name="VirtualList">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { useThrottle } from '@/composables/useGlobal';

// Props定义
interface Props {
  items: any[];
  itemHeight: number;
  containerHeight: string;
  buffer?: number; // 缓冲区大小
  keyField?: string; // 用作key的字段名
}

const props = withDefaults(defineProps<Props>(), {
  buffer: 5,
  keyField: 'id'
});

// 响应式数据
const containerRef = ref<HTMLElement>();
const scrollTop = ref(0);
const containerClientHeight = ref(0);

const { throttle } = useThrottle();

// 计算属性
const totalHeight = computed(() => props.items.length * props.itemHeight);

const visibleCount = computed(() => {
  return Math.ceil(containerClientHeight.value / props.itemHeight);
});

const startIndex = computed(() => {
  const index = Math.floor(scrollTop.value / props.itemHeight);
  return Math.max(0, index - props.buffer);
});

const endIndex = computed(() => {
  const index = startIndex.value + visibleCount.value + props.buffer * 2;
  return Math.min(props.items.length - 1, index);
});

const visibleItems = computed(() => {
  const items = [];
  for (let i = startIndex.value; i <= endIndex.value; i++) {
    if (props.items[i]) {
      items.push({
        data: props.items[i],
        index: i
      });
    }
  }
  return items;
});

const offsetY = computed(() => startIndex.value * props.itemHeight);

// 方法
function getItemKey(item: any): string | number {
  if (typeof item === 'object' && item !== null) {
    return item[props.keyField] || item.id || Math.random();
  }
  return item;
}

const handleScroll = throttle((event: Event) => {
  const target = event.target as HTMLElement;
  scrollTop.value = target.scrollTop;
}, 16); // 约60fps

function updateContainerHeight() {
  if (containerRef.value) {
    containerClientHeight.value = containerRef.value.clientHeight;
  }
}

// 监听容器高度变化
const resizeObserver = ref<ResizeObserver>();

function createResizeObserver() {
  if (!containerRef.value) return;

  resizeObserver.value = new ResizeObserver(() => {
    updateContainerHeight();
  });

  resizeObserver.value.observe(containerRef.value);
}

function destroyResizeObserver() {
  if (resizeObserver.value) {
    resizeObserver.value.disconnect();
    resizeObserver.value = undefined;
  }
}

// 监听items变化，重置滚动位置
watch(() => props.items, () => {
  scrollTop.value = 0;
  if (containerRef.value) {
    containerRef.value.scrollTop = 0;
  }
});

// 生命周期
onMounted(() => {
  updateContainerHeight();
  
  if ('ResizeObserver' in window) {
    createResizeObserver();
  }
});

onUnmounted(() => {
  destroyResizeObserver();
});

// 暴露方法给父组件
defineExpose({
  scrollTo: (index: number) => {
    if (containerRef.value) {
      const targetScrollTop = index * props.itemHeight;
      containerRef.value.scrollTop = targetScrollTop;
      scrollTop.value = targetScrollTop;
    }
  },
  scrollToTop: () => {
    if (containerRef.value) {
      containerRef.value.scrollTop = 0;
      scrollTop.value = 0;
    }
  }
});
</script>

<style scoped>
.virtual-list-container {
  position: relative;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.virtual-list-phantom {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: -1;
}

.virtual-list-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.virtual-list-item {
  box-sizing: border-box;
}

/* 滚动条样式优化 */
.virtual-list-container::-webkit-scrollbar {
  width: 4px;
}

.virtual-list-container::-webkit-scrollbar-track {
  background: transparent;
}

.virtual-list-container::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

.virtual-list-container::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}
</style>
