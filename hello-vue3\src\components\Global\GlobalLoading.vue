<template>
  <van-overlay 
    :show="isGlobalLoading" 
    class="global-loading-overlay"
    :z-index="9999"
  >
    <div class="loading-content">
      <van-loading 
        type="spinner" 
        size="24px" 
        color="#1989fa"
        text-color="#1989fa"
      >
        加载中...
      </van-loading>
    </div>
  </van-overlay>
</template>

<script lang="ts" setup name="GlobalLoading">
import { useLoading } from '@/composables/useGlobal';

const { isGlobalLoading } = useLoading();
</script>

<style scoped>
.global-loading-overlay {
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}
</style>
