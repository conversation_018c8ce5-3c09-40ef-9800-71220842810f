<template>
  <div>
    <!-- Vant底部导航栏组件，使用v-model绑定当前选中项索引 -->
    <van-tabbar v-model="active">
      <!-- 首页标签，点击导航到homeTop路由 -->
      <van-tabbar-item icon="home-o" to="homeTop">首页</van-tabbar-item>

      <!-- 购物车标签，显示商品数量徽章，点击导航到ShopCart路由 -->
      <van-tabbar-item icon="cart-o" :badge="goodsNum" to="ShopCart">购物车</van-tabbar-item>

      <!-- 个人中心标签 -->
      <van-tabbar-item icon="user-o" to="profile">我的</van-tabbar-item>

      <!-- 设置标签 -->
      <van-tabbar-item icon="setting-o" to="settings">设置</van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { useStore } from '@/store';  // 导入Pinia购物车store

// 记录当前激活的标签索引（0:首页 1:购物车 2:退出）
const active = ref(0);

// 获取购物车store实例
const store = useStore();

// 计算属性：动态获取购物车中的商品总数
// 从store中获取并响应式更新徽标显示
const goodsNum = computed(() => store.cartItemsCount);

// 移除了loginOut函数，退出登录功能已移至设置页面
</script>