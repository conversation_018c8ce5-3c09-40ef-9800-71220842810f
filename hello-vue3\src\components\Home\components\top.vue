<template>
    <div>
      <!-- 搜索栏 -->
      <div class="search-bar">
        <van-search
          placeholder="搜索商品"
          readonly
          @click="goToSearch"
        />
      </div>

      <!-- 轮播图 -->
      <van-swipe :autoplay="3000" lazy-render>
        <van-swipe-item v-for="image in images" :key="image">
          <img :src="image.img" />
        </van-swipe-item>
      </van-swipe>
    </div>
<h2>热门视频</h2> 
<div class="hotVideos">
  <van-grid :border="false" :column-num="2">
  <van-grid-item v-for="(item,index) in hotVideos" :key="index" @click="goToProduct(item)">
    <LazyImage
      :src="item.img"
      :alt="item.details"
      width="100%"
      height="120px"
    />
    <div class="product-info">
      <div class="product-title">{{ item.details }}</div>
      <div class="product-actions">
        <span class="product-price">¥99.9</span>
        <van-button
          size="mini"
          type="primary"
          @click.stop="tocart(item)"
        >
          加购物车
        </van-button>
      </div>
    </div>
  </van-grid-item>
</van-grid>
    </div>
  </template>
  
<script lang="ts" setup name="top">
import http from '@/axios/axiosConfig';
import { ref } from 'vue';
import { Toast } from 'vant';
import { useStore } from '@/store';
import { useRouter } from 'vue-router';
import LazyImage from '@/components/Common/LazyImage.vue';
const images = ref<any>();
const hotVideos = ref<any>();
const store = useStore();
const router = useRouter();

// 获取首页轮播和热门视频的资源
function getSource() {
    http.get('/api/homePhoto').then((res: any) => {
    if (res.data.code==0) {
      images.value = res.data.data[0].bannerList;
      hotVideos.value = res.data.data[1].videoList; // 只有 data.images 存在时才有效
    }
  })
  .catch((err: any) => {
    console.log(err);
  });
}
getSource();
//定义添加商品到购物车的方法
function tocart(item:any){
  Toast.success('成功添加到购物车');
  store.addToCart(item)
}

//跳转到商品详情页
function goToProduct(item: any) {
  router.push(`/product/${item.id}`)
}

//跳转到搜索页面
function goToSearch() {
  router.push('/search')
}

  </script>
  
  <style scoped>
.search-bar {
  padding: 8px 16px;
  background: white;
}

img{
    width: 100%;
 }
 h2{
    text-align: center;
 }
.hotVideos{
    margin-bottom: 3rem;
 }

.product-info {
  padding: 8px;
  text-align: left;
}

.product-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-price {
  font-size: 16px;
  font-weight: bold;
  color: #ff6b35;
}

  </style>
  