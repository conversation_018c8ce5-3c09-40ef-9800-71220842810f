<template>
  <div class="order-list-page">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="我的订单"
      left-text="返回"
      left-arrow
      @click-left="goBack"
    />

    <!-- 订单状态标签 -->
    <van-tabs v-model:active="activeTab" @change="onTabChange">
      <van-tab title="全部" name="all" />
      <van-tab title="待付款" name="pending" />
      <van-tab title="待发货" name="paid" />
      <van-tab title="待收货" name="shipped" />
      <van-tab title="已完成" name="completed" />
    </van-tabs>

    <!-- 订单列表 -->
    <div class="order-list">
      <!-- 空状态 -->
      <div v-if="filteredOrders.length === 0" class="empty-state">
        <van-empty 
          image="search" 
          description="暂无订单"
        >
          <van-button type="primary" @click="goToHome">
            去购物
          </van-button>
        </van-empty>
      </div>

      <!-- 订单项 -->
      <div 
        v-for="order in filteredOrders" 
        :key="order.id" 
        class="order-item"
        @click="goToOrderDetail(order.id)"
      >
        <!-- 订单头部 -->
        <div class="order-header">
          <div class="order-info">
            <span class="order-number">订单号：{{ order.orderNumber }}</span>
            <span class="order-time">{{ formatTime(order.createTime) }}</span>
          </div>
          <div class="order-status" :class="getStatusClass(order.status)">
            {{ getStatusText(order.status) }}
          </div>
        </div>

        <!-- 商品列表 -->
        <div class="order-goods">
          <div 
            v-for="item in order.items.slice(0, 3)" 
            :key="item.id" 
            class="goods-item"
          >
            <LazyImage 
              :src="item.img" 
              :alt="item.title"
              width="60px"
              height="60px"
              class="goods-image"
            />
            <div class="goods-info">
              <div class="goods-title">{{ item.title }}</div>
              <div class="goods-spec" v-if="item.specifications?.length">
                {{ formatSpecifications(item.specifications) }}
              </div>
              <div class="goods-price">¥{{ item.price }} × {{ item.quantity }}</div>
            </div>
          </div>
          
          <!-- 更多商品提示 -->
          <div v-if="order.items.length > 3" class="more-goods">
            还有{{ order.items.length - 3 }}件商品
          </div>
        </div>

        <!-- 订单金额 -->
        <div class="order-amount">
          <span class="amount-label">实付款：</span>
          <span class="amount-value">¥{{ order.finalAmount.toFixed(2) }}</span>
        </div>

        <!-- 订单操作 -->
        <div class="order-actions">
          <van-button 
            v-if="order.status === 'pending'" 
            size="small" 
            type="danger"
            @click.stop="payOrder(order)"
          >
            立即付款
          </van-button>
          
          <van-button 
            v-if="order.status === 'pending'" 
            size="small" 
            plain
            @click.stop="cancelOrder(order.id)"
          >
            取消订单
          </van-button>
          
          <van-button 
            v-if="order.status === 'shipped'" 
            size="small" 
            type="primary"
            @click.stop="confirmReceive(order.id)"
          >
            确认收货
          </van-button>
          
          <van-button 
            v-if="order.status === 'completed'" 
            size="small" 
            plain
            @click.stop="buyAgain(order)"
          >
            再次购买
          </van-button>
          
          <van-button 
            size="small" 
            plain
            @click.stop="goToOrderDetail(order.id)"
          >
            查看详情
          </van-button>
        </div>
      </div>
    </div>

    <!-- 取消订单确认弹窗 -->
    <van-dialog
      v-model:show="showCancelDialog"
      title="取消订单"
      message="确定要取消这个订单吗？"
      show-cancel-button
      @confirm="confirmCancel"
    />
  </div>
</template>

<script lang="ts" setup name="OrderList">
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from '@/store';
import { Toast } from 'vant';
import type { Order, OrderStatus } from '@/types';
import LazyImage from '@/components/Common/LazyImage.vue';

const route = useRoute();
const router = useRouter();
const store = useStore();

// 响应式数据
const activeTab = ref('all');
const showCancelDialog = ref(false);
const cancelOrderId = ref<number | null>(null);

// 计算属性
const orders = computed(() => store.orders);

const filteredOrders = computed(() => {
  if (activeTab.value === 'all') {
    return orders.value;
  }
  return store.ordersByStatus(activeTab.value);
});

// 方法
function goBack() {
  router.go(-1);
}

function goToHome() {
  router.push('/home/<USER>');
}

function goToOrderDetail(orderId: number) {
  router.push(`/order/${orderId}`);
}

function onTabChange(name: string) {
  activeTab.value = name;
}

function getStatusText(status: OrderStatus): string {
  const statusMap = {
    pending: '待付款',
    paid: '待发货',
    shipped: '待收货',
    delivered: '已送达',
    completed: '已完成',
    cancelled: '已取消',
    refunded: '已退款'
  };
  return statusMap[status] || '未知状态';
}

function getStatusClass(status: OrderStatus): string {
  const classMap = {
    pending: 'status-pending',
    paid: 'status-paid',
    shipped: 'status-shipped',
    delivered: 'status-delivered',
    completed: 'status-completed',
    cancelled: 'status-cancelled',
    refunded: 'status-refunded'
  };
  return classMap[status] || '';
}

function formatTime(timeStr: string): string {
  const date = new Date(timeStr);
  return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
}

function formatSpecifications(specs: any[]): string {
  return specs.map(spec => `${spec.specName}:${spec.optionValue}`).join(', ');
}

function payOrder(order: Order) {
  // 跳转到付款页面
  router.push('/payment');
}

function cancelOrder(orderId: number) {
  cancelOrderId.value = orderId;
  showCancelDialog.value = true;
}

function confirmCancel() {
  if (cancelOrderId.value) {
    // 这里应该调用API取消订单
    Toast.success('订单已取消');
    cancelOrderId.value = null;
  }
  showCancelDialog.value = false;
}

function confirmReceive(orderId: number) {
  // 这里应该调用API确认收货
  Toast.success('确认收货成功');
}

function buyAgain(order: Order) {
  // 将订单商品重新添加到购物车
  order.items.forEach(item => {
    store.addToCart({
      id: item.productId,
      title: item.title,
      details: item.title,
      img: item.img,
      price: item.price,
      stock: 100,
      category: '商品',
      categoryId: 1,
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    });
  });
  
  Toast.success('商品已添加到购物车');
  router.push('/home/<USER>');
}

// 初始化
function initOrders() {
  // 如果没有订单，创建一些模拟订单
  if (orders.value.length === 0) {
    const mockOrders: Order[] = [
      {
        id: 1,
        orderNumber: '202401150001',
        userId: 1,
        status: 'pending',
        items: [
          {
            id: 1,
            productId: 1,
            title: '超级会员',
            img: 'https://file.xdclass.net/video/2021/1-lbt/VIP/vip1299.png',
            price: 99.9,
            quantity: 1,
            subtotal: 99.9
          }
        ],
        totalAmount: 99.9,
        shippingFee: 0,
        finalAmount: 99.9,
        shippingAddress: {
          id: 1,
          userId: 1,
          name: '张三',
          phone: '13800138000',
          province: '北京市',
          city: '北京市',
          district: '朝阳区',
          detail: '某某街道123号',
          isDefault: true,
          createTime: new Date().toISOString(),
          updateTime: new Date().toISOString()
        },
        paymentMethod: 'wechat',
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString()
      },
      {
        id: 2,
        orderNumber: '202401150002',
        userId: 1,
        status: 'completed',
        items: [
          {
            id: 2,
            productId: 2,
            title: '工业级微服务项目',
            img: 'https://file.xdclass.net/video/2021/62-paas/lbt-paas.png',
            price: 199.9,
            quantity: 1,
            subtotal: 199.9
          }
        ],
        totalAmount: 199.9,
        shippingFee: 10,
        finalAmount: 209.9,
        shippingAddress: {
          id: 1,
          userId: 1,
          name: '张三',
          phone: '13800138000',
          province: '北京市',
          city: '北京市',
          district: '朝阳区',
          detail: '某某街道123号',
          isDefault: true,
          createTime: new Date().toISOString(),
          updateTime: new Date().toISOString()
        },
        paymentMethod: 'alipay',
        paymentTime: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
        completedTime: new Date().toISOString(),
        createTime: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
        updateTime: new Date().toISOString()
      }
    ];

    mockOrders.forEach(order => store.addOrder(order));
  }
}

onMounted(() => {
  // 从路由参数获取状态
  const status = route.query.status as string;
  if (status) {
    activeTab.value = status;
  }
  
  initOrders();
});
</script>

<style scoped>
.order-list-page {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.order-list {
  padding: 0 16px 20px;
}

.empty-state {
  padding: 60px 20px;
}

.order-item {
  background: white;
  border-radius: 8px;
  margin-bottom: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s;
}

.order-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f5f5f5;
}

.order-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.order-number {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.order-time {
  font-size: 12px;
  color: #999;
}

.order-status {
  font-size: 14px;
  font-weight: 500;
}

.status-pending { color: #ff6b35; }
.status-paid { color: #1989fa; }
.status-shipped { color: #07c160; }
.status-delivered { color: #07c160; }
.status-completed { color: #969799; }
.status-cancelled { color: #ee0a24; }
.status-refunded { color: #ff976a; }

.order-goods {
  margin-bottom: 12px;
}

.goods-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.goods-item:last-child {
  margin-bottom: 0;
}

.goods-image {
  border-radius: 4px;
  margin-right: 12px;
  flex-shrink: 0;
}

.goods-info {
  flex: 1;
  min-width: 0;
}

.goods-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.goods-spec {
  font-size: 12px;
  color: #999;
  margin-bottom: 4px;
}

.goods-price {
  font-size: 14px;
  color: #666;
}

.more-goods {
  font-size: 12px;
  color: #999;
  text-align: center;
  padding: 8px 0;
}

.order-amount {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 12px;
  padding-top: 8px;
  border-top: 1px solid #f5f5f5;
}

.amount-label {
  font-size: 14px;
  color: #666;
}

.amount-value {
  font-size: 16px;
  font-weight: bold;
  color: #ff6b35;
  margin-left: 4px;
}

.order-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
</style>
