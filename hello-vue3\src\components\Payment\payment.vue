<template>
  <div class="payment-container">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="付款"
      left-text="返回"
      left-arrow
      @click-left="goBack"
    />

    <!-- 订单信息区域 -->
    <div class="order-info">
      <div class="order-header">
        <h3>订单详情</h3>
        <div class="order-number">订单号：{{ orderNumber }}</div>
      </div>
      
      <!-- 商品列表 -->
      <div class="goods-list">
        <div class="goods-item" v-for="(item, index) in orderGoods" :key="index">
          <img :src="item.img" alt="" class="goods-image">
          <div class="goods-info">
            <div class="goods-title">{{ item.title }}</div>
            <div class="goods-count">数量：{{ item.cartCount }}</div>
          </div>
        </div>
      </div>

      <!-- 价格信息 -->
      <div class="price-info">
        <div class="price-item">
          <span>商品总价</span>
          <span>¥{{ totalPrice.toFixed(2) }}</span>
        </div>
        <div class="price-item">
          <span>运费</span>
          <span>¥{{ shippingFee.toFixed(2) }}</span>
        </div>
        <div class="price-item total">
          <span>实付款</span>
          <span class="total-price">¥{{ finalPrice.toFixed(2) }}</span>
        </div>
      </div>
    </div>

    <!-- 付款方式选择 -->
    <div class="payment-methods">
      <h3>选择付款方式</h3>
      <van-radio-group v-model="selectedPayment">
        <van-cell-group>
          <van-cell clickable @click="selectedPayment = 'wechat'">
            <template #title>
              <div class="payment-option">
                <van-icon name="wechat" size="24" color="#07c160" />
                <span>微信支付</span>
              </div>
            </template>
            <template #right-icon>
              <van-radio name="wechat" />
            </template>
          </van-cell>
          
          <van-cell clickable @click="selectedPayment = 'alipay'">
            <template #title>
              <div class="payment-option">
                <van-icon name="alipay" size="24" color="#1677ff" />
                <span>支付宝</span>
              </div>
            </template>
            <template #right-icon>
              <van-radio name="alipay" />
            </template>
          </van-cell>
          
          <van-cell clickable @click="selectedPayment = 'balance'">
            <template #title>
              <div class="payment-option">
                <van-icon name="balance-o" size="24" color="#ff6b35" />
                <span>余额支付</span>
              </div>
            </template>
            <template #right-icon>
              <van-radio name="balance" />
            </template>
          </van-cell>
        </van-cell-group>
      </van-radio-group>
    </div>

    <!-- 底部付款按钮 -->
    <div class="payment-footer">
      <div class="payment-amount">
        <span>需付款：</span>
        <span class="amount">¥{{ finalPrice.toFixed(2) }}</span>
      </div>
      <van-button 
        type="primary" 
        size="large" 
        class="pay-button"
        :loading="paymentLoading"
        @click="handlePayment"
      >
        {{ paymentLoading ? '支付中...' : '立即付款' }}
      </van-button>
    </div>

    <!-- 支付成功弹窗 -->
    <van-dialog
      v-model:show="showSuccessDialog"
      title="支付成功"
      :show-cancel-button="false"
      confirm-button-text="返回首页"
      @confirm="goToHome"
    >
      <div class="success-content">
        <van-icon name="success" size="60" color="#07c160" />
        <p>支付成功！订单已提交</p>
        <p class="order-tip">订单号：{{ orderNumber }}</p>
      </div>
    </van-dialog>
  </div>
</template>

<script lang="ts" setup name="payment">
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from '@/store';
import { Toast } from 'vant';

const route = useRoute();
const router = useRouter();
const store = useStore();

// 响应式数据
const selectedPayment = ref('wechat'); // 默认选择微信支付
const paymentLoading = ref(false);
const showSuccessDialog = ref(false);
const orderNumber = ref('');
const shippingFee = ref(0); // 运费

// 订单商品数据
const orderGoods = ref<any[]>([]);

// 计算属性
const totalPrice = computed(() => {
  return orderGoods.value.reduce((total, item) => {
    // 假设每个商品价格为99.9元（实际项目中应该从商品数据中获取）
    return total + (99.9 * item.cartCount);
  }, 0);
});

const finalPrice = computed(() => {
  return totalPrice.value + shippingFee.value;
});

// 生成订单号
function generateOrderNumber() {
  const now = new Date();
  const timestamp = now.getTime();
  const random = Math.floor(Math.random() * 1000);
  return `${timestamp}${random}`;
}

// 初始化订单数据
function initOrderData() {
  // 从购物车获取商品数据
  orderGoods.value = [...store.cartItems];
  orderNumber.value = generateOrderNumber();

  // 如果没有商品，返回购物车
  if (orderGoods.value.length === 0) {
    Toast.fail('购物车为空');
    router.push('/home/<USER>');
  }
}

// 返回上一页
function goBack() {
  router.go(-1);
}

// 处理付款
async function handlePayment() {
  if (!selectedPayment.value) {
    Toast.fail('请选择付款方式');
    return;
  }

  paymentLoading.value = true;
  
  try {
    // 模拟支付请求
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 支付成功后清空购物车
    store.clearCart();
    
    // 显示成功弹窗
    showSuccessDialog.value = true;
    
  } catch (error) {
    Toast.fail('支付失败，请重试');
  } finally {
    paymentLoading.value = false;
  }
}

// 跳转到首页
function goToHome() {
  showSuccessDialog.value = false;
  router.push('/home/<USER>');
}

// 组件挂载时初始化数据
onMounted(() => {
  initOrderData();
});
</script>

<style scoped>
.payment-container {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 80px;
}

.order-info {
  background: white;
  margin: 10px;
  border-radius: 8px;
  padding: 16px;
}

.order-header {
  border-bottom: 1px solid #eee;
  padding-bottom: 12px;
  margin-bottom: 16px;
}

.order-header h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
}

.order-number {
  font-size: 14px;
  color: #666;
}

.goods-list {
  margin-bottom: 16px;
}

.goods-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}

.goods-item:last-child {
  border-bottom: none;
}

.goods-image {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  margin-right: 12px;
}

.goods-info {
  flex: 1;
}

.goods-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
}

.goods-count {
  font-size: 12px;
  color: #666;
}

.price-info {
  border-top: 1px solid #eee;
  padding-top: 12px;
}

.price-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.price-item.total {
  font-weight: 600;
  font-size: 16px;
  color: #333;
}

.total-price {
  color: #ff6b35;
}

.payment-methods {
  background: white;
  margin: 10px;
  border-radius: 8px;
  padding: 16px;
}

.payment-methods h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.payment-option {
  display: flex;
  align-items: center;
  gap: 12px;
}

.payment-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 16px;
  border-top: 1px solid #eee;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.payment-amount {
  font-size: 16px;
  font-weight: 600;
}

.amount {
  color: #ff6b35;
  font-size: 18px;
}

.pay-button {
  width: 120px;
}

.success-content {
  text-align: center;
  padding: 20px;
}

.success-content p {
  margin: 12px 0;
}

.order-tip {
  font-size: 12px;
  color: #666;
}
</style>
