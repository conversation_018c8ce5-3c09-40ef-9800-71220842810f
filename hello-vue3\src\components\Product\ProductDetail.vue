<template>
  <div class="product-detail">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="商品详情"
      left-text="返回"
      left-arrow
      @click-left="goBack"
    >
      <template #right>
        <van-icon 
          :name="isFavorited ? 'star' : 'star-o'" 
          :color="isFavorited ? '#ff6b35' : '#969799'"
          size="20"
          @click="toggleFavorite"
        />
      </template>
    </van-nav-bar>

    <!-- 商品图片轮播 -->
    <div class="product-images">
      <van-swipe :autoplay="3000" indicator-color="white">
        <van-swipe-item v-for="(image, index) in productImages" :key="index">
          <img :src="image" alt="商品图片" @click="previewImage(index)" />
        </van-swipe-item>
      </van-swipe>
    </div>

    <!-- 商品基本信息 -->
    <div class="product-info">
      <div class="price-section">
        <span class="current-price">¥{{ product.price }}</span>
        <span v-if="product.originalPrice" class="original-price">¥{{ product.originalPrice }}</span>
      </div>
      
      <h2 class="product-title">{{ product.title }}</h2>
      <p class="product-subtitle">{{ product.details }}</p>
      
      <div class="product-stats">
        <span class="sales">已售{{ product.sales || 0 }}件</span>
        <span class="stock">库存{{ product.stock || 0 }}件</span>
        <div class="rating">
          <van-rate v-model="product.rating" readonly size="14" />
          <span class="rating-text">({{ product.rating || 5.0 }})</span>
        </div>
      </div>
    </div>

    <!-- 规格选择 -->
    <div class="specifications" v-if="product.specifications?.length">
      <div class="spec-title">选择规格</div>
      <div v-for="spec in product.specifications" :key="spec.id" class="spec-group">
        <div class="spec-name">{{ spec.name }}</div>
        <div class="spec-options">
          <div
            v-for="option in spec.options"
            :key="option.id"
            class="spec-option"
            :class="{ active: selectedSpecs[spec.id] === option.id }"
            @click="selectSpec(spec.id, option.id, option.value)"
          >
            {{ option.value }}
          </div>
        </div>
      </div>
    </div>

    <!-- 商品详情 -->
    <div class="product-description">
      <div class="section-title">商品详情</div>
      <div class="description-content">
        {{ product.description || '暂无详细描述' }}
      </div>
    </div>

    <!-- 用户评价 -->
    <div class="product-reviews" v-if="product.reviews?.length">
      <div class="section-title">
        用户评价 ({{ product.reviews.length }})
      </div>
      <div class="reviews-list">
        <div v-for="review in displayReviews" :key="review.id" class="review-item">
          <div class="review-header">
            <img :src="review.userAvatar || '/default-avatar.png'" class="user-avatar" />
            <div class="user-info">
              <div class="user-name">{{ review.userName }}</div>
              <van-rate v-model="review.rating" readonly size="12" />
            </div>
            <div class="review-time">{{ formatTime(review.createTime) }}</div>
          </div>
          <div class="review-content">{{ review.content }}</div>
          <div v-if="review.images?.length" class="review-images">
            <img 
              v-for="(img, index) in review.images" 
              :key="index" 
              :src="img" 
              @click="previewReviewImage(review.images, index)"
            />
          </div>
        </div>
      </div>
      <div v-if="product.reviews.length > 3" class="view-more" @click="showAllReviews = !showAllReviews">
        {{ showAllReviews ? '收起评价' : '查看更多评价' }}
      </div>
    </div>

    <!-- 底部操作栏 -->
    <div class="bottom-actions">
      <div class="action-buttons">
        <van-button 
          class="add-cart-btn" 
          type="warning" 
          @click="addToCart"
          :loading="addingToCart"
        >
          加入购物车
        </van-button>
        <van-button 
          class="buy-now-btn" 
          type="danger" 
          @click="buyNow"
          :loading="buyingNow"
        >
          立即购买
        </van-button>
      </div>
    </div>

    <!-- 规格选择弹窗 -->
    <van-popup v-model:show="showSpecPopup" position="bottom" round>
      <div class="spec-popup">
        <div class="popup-header">
          <h3>选择商品规格</h3>
          <van-icon name="cross" @click="showSpecPopup = false" />
        </div>
        <div class="popup-product-info">
          <img :src="product.img" class="popup-product-image" />
          <div class="popup-product-details">
            <div class="popup-price">¥{{ product.price }}</div>
            <div class="popup-stock">库存{{ product.stock }}件</div>
          </div>
        </div>
        <!-- 规格选择内容 -->
        <div class="popup-specs">
          <div v-for="spec in product.specifications" :key="spec.id" class="popup-spec-group">
            <div class="popup-spec-name">{{ spec.name }}</div>
            <div class="popup-spec-options">
              <div
                v-for="option in spec.options"
                :key="option.id"
                class="popup-spec-option"
                :class="{ active: selectedSpecs[spec.id] === option.id }"
                @click="selectSpec(spec.id, option.id, option.value)"
              >
                {{ option.value }}
              </div>
            </div>
          </div>
        </div>
        <!-- 数量选择 -->
        <div class="quantity-section">
          <span class="quantity-label">数量</span>
          <van-stepper v-model="quantity" min="1" :max="product.stock" />
        </div>
        <!-- 确认按钮 -->
        <div class="popup-actions">
          <van-button 
            type="danger" 
            block 
            @click="confirmAddToCart"
            :disabled="!canAddToCart"
          >
            确认加入购物车
          </van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script lang="ts" setup name="ProductDetail">
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from '@/store';
import { Toast, ImagePreview } from 'vant';
import type { Product } from '@/types';

const route = useRoute();
const router = useRouter();
const store = useStore();

// 响应式数据
const product = ref<Product>({
  id: 0,
  title: '',
  details: '',
  img: '',
  price: 0,
  stock: 0,
  category: '',
  categoryId: 0,
  createTime: '',
  updateTime: ''
});

const selectedSpecs = ref<Record<number, number>>({});
const quantity = ref(1);
const showSpecPopup = ref(false);
const showAllReviews = ref(false);
const addingToCart = ref(false);
const buyingNow = ref(false);

// 计算属性
const productImages = computed(() => {
  return product.value.images?.length ? product.value.images : [product.value.img];
});

const isFavorited = computed(() => {
  return store.isFavorite(product.value.id);
});

const displayReviews = computed(() => {
  if (!product.value.reviews) return [];
  return showAllReviews.value ? product.value.reviews : product.value.reviews.slice(0, 3);
});

const canAddToCart = computed(() => {
  // 检查是否所有必需规格都已选择
  if (product.value.specifications?.length) {
    return product.value.specifications.every(spec => selectedSpecs.value[spec.id]);
  }
  return true;
});

// 方法
function goBack() {
  router.go(-1);
}

function toggleFavorite() {
  if (isFavorited.value) {
    store.removeFromFavorites(product.value.id);
    Toast.success('已取消收藏');
  } else {
    store.addToFavorites(product.value);
    Toast.success('已添加到收藏');
  }
}

function selectSpec(specId: number, optionId: number, optionValue: string) {
  selectedSpecs.value[specId] = optionId;
}

function previewImage(index: number) {
  ImagePreview({
    images: productImages.value,
    startPosition: index,
  });
}

function previewReviewImage(images: string[], index: number) {
  ImagePreview({
    images,
    startPosition: index,
  });
}

function addToCart() {
  if (product.value.specifications?.length && !canAddToCart.value) {
    showSpecPopup.value = true;
    return;
  }
  
  addingToCart.value = true;
  setTimeout(() => {
    store.addToCart({
      ...product.value,
      selectedSpecs: getSelectedSpecsInfo()
    });
    Toast.success('已添加到购物车');
    addingToCart.value = false;
  }, 500);
}

function buyNow() {
  if (product.value.specifications?.length && !canAddToCart.value) {
    showSpecPopup.value = true;
    return;
  }
  
  buyingNow.value = true;
  setTimeout(() => {
    // 先添加到购物车
    store.addToCart({
      ...product.value,
      selectedSpecs: getSelectedSpecsInfo()
    });
    // 跳转到付款页面
    router.push('/payment');
    buyingNow.value = false;
  }, 500);
}

function confirmAddToCart() {
  showSpecPopup.value = false;
  addToCart();
}

function getSelectedSpecsInfo() {
  if (!product.value.specifications) return [];
  
  return product.value.specifications.map(spec => {
    const selectedOptionId = selectedSpecs.value[spec.id];
    const selectedOption = spec.options.find(opt => opt.id === selectedOptionId);
    return {
      specId: spec.id,
      specName: spec.name,
      optionId: selectedOptionId,
      optionValue: selectedOption?.value || ''
    };
  });
}

function formatTime(timeStr: string) {
  const date = new Date(timeStr);
  return date.toLocaleDateString();
}

// 初始化商品数据
function initProduct() {
  const productId = Number(route.params.id);
  
  // 这里应该从API获取商品详情，现在使用模拟数据
  const mockProduct: Product = {
    id: productId,
    title: '高品质商品',
    details: '这是一个非常优质的商品，值得购买',
    img: 'https://file.xdclass.net/video/2021/1-lbt/VIP/vip1299.png',
    images: [
      'https://file.xdclass.net/video/2021/1-lbt/VIP/vip1299.png',
      'https://file.xdclass.net/video/2021/62-paas/lbt-paas.png',
      'https://file.xdclass.net/video/2021/aliyun/03lbt.png'
    ],
    price: 99.9,
    originalPrice: 199.9,
    stock: 100,
    sales: 1234,
    category: '电子产品',
    categoryId: 1,
    rating: 4.8,
    description: '这是一个非常优质的商品，采用最新技术制造，品质保证，性价比极高。适合各种场景使用，是您的不二选择。',
    specifications: [
      {
        id: 1,
        name: '颜色',
        options: [
          { id: 1, value: '红色', price: 0, stock: 50 },
          { id: 2, value: '蓝色', price: 0, stock: 30 },
          { id: 3, value: '黑色', price: 10, stock: 20 }
        ]
      },
      {
        id: 2,
        name: '尺寸',
        options: [
          { id: 4, value: 'S', price: 0, stock: 40 },
          { id: 5, value: 'M', price: 0, stock: 35 },
          { id: 6, value: 'L', price: 5, stock: 25 }
        ]
      }
    ],
    reviews: [
      {
        id: 1,
        userId: 1,
        userName: '用户A',
        userAvatar: '',
        rating: 5,
        content: '商品质量很好，物流也很快，非常满意！',
        images: [],
        createTime: '2024-01-15T10:30:00Z'
      },
      {
        id: 2,
        userId: 2,
        userName: '用户B',
        rating: 4,
        content: '整体不错，就是包装可以再精美一些。',
        createTime: '2024-01-14T15:20:00Z'
      }
    ],
    createTime: '2024-01-01T00:00:00Z',
    updateTime: '2024-01-15T12:00:00Z'
  };
  
  product.value = mockProduct;
}

onMounted(() => {
  initProduct();
});
</script>

<style scoped>
.product-detail {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 80px;
}

.product-images {
  background: white;
}

.product-images img {
  width: 100%;
  height: 300px;
  object-fit: cover;
}

.product-info {
  background: white;
  padding: 16px;
  margin-bottom: 10px;
}

.price-section {
  margin-bottom: 12px;
}

.current-price {
  font-size: 24px;
  font-weight: bold;
  color: #ff6b35;
  margin-right: 8px;
}

.original-price {
  font-size: 14px;
  color: #969799;
  text-decoration: line-through;
}

.product-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.product-subtitle {
  font-size: 14px;
  color: #666;
  margin: 0 0 12px 0;
}

.product-stats {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 12px;
  color: #969799;
}

.rating {
  display: flex;
  align-items: center;
  gap: 4px;
}

.specifications {
  background: white;
  padding: 16px;
  margin-bottom: 10px;
}

.spec-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
}

.spec-group {
  margin-bottom: 16px;
}

.spec-name {
  font-size: 14px;
  margin-bottom: 8px;
  color: #333;
}

.spec-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.spec-option {
  padding: 6px 12px;
  border: 1px solid #eee;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
}

.spec-option.active {
  border-color: #ff6b35;
  background-color: #fff7f0;
  color: #ff6b35;
}

.product-description,
.product-reviews {
  background: white;
  padding: 16px;
  margin-bottom: 10px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
}

.description-content {
  font-size: 14px;
  line-height: 1.6;
  color: #666;
}

.review-item {
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}

.review-item:last-child {
  border-bottom: none;
}

.review-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 8px;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 14px;
  margin-bottom: 2px;
}

.review-time {
  font-size: 12px;
  color: #969799;
}

.review-content {
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 8px;
}

.review-images {
  display: flex;
  gap: 8px;
}

.review-images img {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  object-fit: cover;
  cursor: pointer;
}

.view-more {
  text-align: center;
  color: #ff6b35;
  font-size: 14px;
  cursor: pointer;
  padding: 8px 0;
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 12px 16px;
  border-top: 1px solid #eee;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.add-cart-btn,
.buy-now-btn {
  flex: 1;
  height: 44px;
}

/* 弹窗样式 */
.spec-popup {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.popup-product-info {
  display: flex;
  margin-bottom: 20px;
}

.popup-product-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  margin-right: 12px;
}

.popup-product-details {
  flex: 1;
}

.popup-price {
  font-size: 18px;
  font-weight: bold;
  color: #ff6b35;
  margin-bottom: 4px;
}

.popup-stock {
  font-size: 14px;
  color: #666;
}

.popup-spec-group {
  margin-bottom: 20px;
}

.popup-spec-name {
  font-size: 14px;
  margin-bottom: 8px;
  font-weight: 500;
}

.popup-spec-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.popup-spec-option {
  padding: 8px 16px;
  border: 1px solid #eee;
  border-radius: 20px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
}

.popup-spec-option.active {
  border-color: #ff6b35;
  background-color: #ff6b35;
  color: white;
}

.quantity-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.quantity-label {
  font-size: 14px;
  font-weight: 500;
}

.popup-actions {
  margin-top: 20px;
}
</style>
