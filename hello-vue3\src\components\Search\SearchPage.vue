<template>
  <div class="search-page">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="搜索商品"
      left-text="返回"
      left-arrow
      @click-left="goBack"
    />

    <!-- 搜索栏 -->
    <div class="search-header">
      <van-search
        v-model="searchKeyword"
        placeholder="搜索商品"
        show-action
        @search="handleSearch"
      >
        <template #action>
          <div @click="handleSearch">搜索</div>
        </template>
      </van-search>
    </div>

    <!-- 搜索历史 -->
    <div v-if="!searchKeyword && searchHistory.length > 0" class="search-history">
      <div class="section-header">
        <span class="section-title">搜索历史</span>
        <van-icon name="delete-o" @click="clearHistory" />
      </div>
      <div class="history-tags">
        <van-tag
          v-for="(item, index) in searchHistory"
          :key="index"
          type="default"
          size="medium"
          class="history-tag"
          @click="searchKeyword = item; handleSearch()"
        >
          {{ item }}
        </van-tag>
      </div>
    </div>

    <!-- 热门搜索 -->
    <div v-if="!searchKeyword" class="hot-search">
      <div class="section-header">
        <span class="section-title">热门搜索</span>
      </div>
      <div class="hot-tags">
        <van-tag
          v-for="(item, index) in hotSearches"
          :key="index"
          type="primary"
          size="medium"
          class="hot-tag"
          @click="searchKeyword = item; handleSearch()"
        >
          {{ item }}
        </van-tag>
      </div>
    </div>

    <!-- 筛选栏 -->
    <div v-if="searchResults.length > 0" class="filter-bar">
      <van-dropdown-menu>
        <van-dropdown-item 
          v-model="sortBy" 
          :options="sortOptions"
          @change="handleSortChange"
        />
        <van-dropdown-item 
          v-model="selectedCategory" 
          :options="categoryOptions"
          @change="handleCategoryChange"
        />
        <van-dropdown-item 
          v-model="priceRange" 
          :options="priceOptions"
          @change="handlePriceChange"
        />
      </van-dropdown-menu>
    </div>

    <!-- 搜索结果 -->
    <div v-if="searchResults.length > 0" class="search-results">
      <div class="results-header">
        <span>找到 {{ totalResults }} 件商品</span>
      </div>
      
      <!-- 使用虚拟滚动优化大量数据渲染 -->
      <VirtualList
        v-if="searchResults.length > 20"
        :items="searchResults"
        :item-height="120"
        container-height="calc(100vh - 200px)"
        class="virtual-product-list"
      >
        <template #default="{ item: product }">
          <div class="product-item" @click="goToProduct(product)">
            <LazyImage
              :src="product.img"
              :alt="product.title"
              width="80px"
              height="80px"
              class="product-image"
            />
            <div class="product-info">
              <div class="product-title" v-html="highlightKeyword(product.title)"></div>
              <div class="product-description">{{ product.details }}</div>
              <div class="product-price">¥{{ product.price }}</div>
              <div class="product-stats">
                <span class="sales">已售{{ product.sales || 0 }}件</span>
                <van-rate v-model="product.rating" readonly size="12" />
              </div>
            </div>
            <div class="product-actions">
              <van-button
                size="small"
                type="primary"
                @click.stop="addToCart(product)"
              >
                加购物车
              </van-button>
            </div>
          </div>
        </template>
      </VirtualList>

      <!-- 少量数据时使用普通列表 -->
      <div v-else class="product-list">
        <div
          v-for="product in searchResults"
          :key="product.id"
          class="product-item"
          @click="goToProduct(product)"
        >
          <LazyImage
            :src="product.img"
            :alt="product.title"
            width="80px"
            height="80px"
            class="product-image"
          />
          <div class="product-info">
            <div class="product-title" v-html="highlightKeyword(product.title)"></div>
            <div class="product-description">{{ product.details }}</div>
            <div class="product-price">¥{{ product.price }}</div>
            <div class="product-stats">
              <span class="sales">已售{{ product.sales || 0 }}件</span>
              <van-rate v-model="product.rating" readonly size="12" />
            </div>
          </div>
          <div class="product-actions">
            <van-button
              size="small"
              type="primary"
              @click.stop="addToCart(product)"
            >
              加购物车
            </van-button>
          </div>
        </div>
      </div>

      <!-- 加载更多 -->
      <div v-if="hasMore" class="load-more">
        <van-button 
          type="default" 
          block 
          :loading="loading"
          @click="loadMore"
        >
          {{ loading ? '加载中...' : '加载更多' }}
        </van-button>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else-if="hasSearched && searchResults.length === 0" class="empty-state">
      <van-empty 
        image="search" 
        description="没有找到相关商品"
      >
        <van-button type="primary" @click="clearSearch">
          重新搜索
        </van-button>
      </van-empty>
    </div>
  </div>
</template>

<script lang="ts" setup name="SearchPage">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useStore } from '@/store';
import { Toast } from 'vant';
import type { Product, SearchParams } from '@/types';
import VirtualList from '@/components/Common/VirtualList.vue';
import LazyImage from '@/components/Common/LazyImage.vue';

const router = useRouter();
const store = useStore();

// 响应式数据
const searchKeyword = ref('');
const searchHistory = ref<string[]>([]);
const searchResults = ref<Product[]>([]);
const hasSearched = ref(false);
const loading = ref(false);
const hasMore = ref(false);
const totalResults = ref(0);
const currentPage = ref(1);

// 筛选条件
const sortBy = ref('default');
const selectedCategory = ref('');
const priceRange = ref('');

// 热门搜索
const hotSearches = ref([
  '超级会员',
  '微服务',
  '阿里云',
  'RabbitMQ',
  'Docker',
  'Spring Boot'
]);

// 筛选选项
const sortOptions = [
  { text: '综合排序', value: 'default' },
  { text: '价格从低到高', value: 'price_asc' },
  { text: '价格从高到低', value: 'price_desc' },
  { text: '销量优先', value: 'sales' },
  { text: '评分优先', value: 'rating' }
];

const categoryOptions = [
  { text: '全部分类', value: '' },
  { text: '电子产品', value: '1' },
  { text: '服装鞋帽', value: '2' },
  { text: '家居用品', value: '3' },
  { text: '图书音像', value: '4' }
];

const priceOptions = [
  { text: '全部价格', value: '' },
  { text: '0-50元', value: '0-50' },
  { text: '50-100元', value: '50-100' },
  { text: '100-200元', value: '100-200' },
  { text: '200元以上', value: '200-' }
];

// 方法
function goBack() {
  router.go(-1);
}

function handleSearch() {
  if (!searchKeyword.value.trim()) {
    Toast.fail('请输入搜索关键词');
    return;
  }

  // 添加到搜索历史
  addToHistory(searchKeyword.value);
  
  // 重置搜索状态
  currentPage.value = 1;
  searchResults.value = [];
  hasSearched.value = true;
  
  // 执行搜索
  performSearch();
}

function performSearch() {
  loading.value = true;
  
  // 模拟搜索API调用
  setTimeout(() => {
    const mockResults = generateMockResults();
    
    if (currentPage.value === 1) {
      searchResults.value = mockResults;
    } else {
      searchResults.value.push(...mockResults);
    }
    
    totalResults.value = 50; // 模拟总数
    hasMore.value = searchResults.value.length < totalResults.value;
    loading.value = false;
  }, 1000);
}

function generateMockResults(): Product[] {
  const baseProducts = [
    {
      id: Date.now() + Math.random(),
      title: `${searchKeyword.value}相关商品`,
      details: '高质量商品，值得购买',
      img: 'https://file.xdclass.net/video/2021/1-lbt/VIP/vip1299.png',
      price: Math.floor(Math.random() * 200) + 50,
      stock: 100,
      sales: Math.floor(Math.random() * 1000),
      rating: 4 + Math.random(),
      category: '电子产品',
      categoryId: 1,
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    }
  ];
  
  return Array.from({ length: 10 }, (_, index) => ({
    ...baseProducts[0],
    id: Date.now() + index,
    title: `${searchKeyword.value}商品 ${index + 1}`,
    price: Math.floor(Math.random() * 200) + 50
  }));
}

function loadMore() {
  currentPage.value++;
  performSearch();
}

function addToHistory(keyword: string) {
  const history = [...searchHistory.value];
  const index = history.indexOf(keyword);
  
  if (index > -1) {
    history.splice(index, 1);
  }
  
  history.unshift(keyword);
  searchHistory.value = history.slice(0, 10); // 最多保存10条
  
  // 保存到本地存储
  localStorage.setItem('searchHistory', JSON.stringify(searchHistory.value));
}

function clearHistory() {
  searchHistory.value = [];
  localStorage.removeItem('searchHistory');
  Toast.success('搜索历史已清空');
}

function clearSearch() {
  searchKeyword.value = '';
  searchResults.value = [];
  hasSearched.value = false;
}

function highlightKeyword(text: string) {
  if (!searchKeyword.value) return text;
  
  const regex = new RegExp(`(${searchKeyword.value})`, 'gi');
  return text.replace(regex, '<mark>$1</mark>');
}

function goToProduct(product: Product) {
  router.push(`/product/${product.id}`);
}

function addToCart(product: Product) {
  store.addToCart(product);
  Toast.success('已添加到购物车');
}

function handleSortChange() {
  // 重新搜索并排序
  currentPage.value = 1;
  searchResults.value = [];
  performSearch();
}

function handleCategoryChange() {
  // 重新搜索并筛选分类
  currentPage.value = 1;
  searchResults.value = [];
  performSearch();
}

function handlePriceChange() {
  // 重新搜索并筛选价格
  currentPage.value = 1;
  searchResults.value = [];
  performSearch();
}

// 初始化
function initSearchHistory() {
  try {
    const history = localStorage.getItem('searchHistory');
    if (history) {
      searchHistory.value = JSON.parse(history);
    }
  } catch (error) {
    console.error('加载搜索历史失败:', error);
  }
}

onMounted(() => {
  initSearchHistory();
});
</script>

<style scoped>
.search-page {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.search-header {
  background: white;
  padding: 8px 16px;
  border-bottom: 1px solid #eee;
}

.search-history,
.hot-search {
  background: white;
  margin: 10px 16px;
  border-radius: 8px;
  padding: 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.history-tags,
.hot-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.history-tag,
.hot-tag {
  cursor: pointer;
}

.filter-bar {
  background: white;
  border-bottom: 1px solid #eee;
}

.search-results {
  padding: 0 16px;
}

.results-header {
  padding: 12px 0;
  font-size: 14px;
  color: #666;
}

.product-list,
.virtual-product-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.virtual-product-list {
  margin-top: 8px;
}

.product-item {
  display: flex;
  background: white;
  border-radius: 8px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.product-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.product-image {
  border-radius: 6px;
  margin-right: 12px;
  flex-shrink: 0;
}

.product-info {
  flex: 1;
  min-width: 0;
}

.product-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-title :deep(mark) {
  background-color: #fff3cd;
  color: #856404;
  padding: 0 2px;
}

.product-description {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-price {
  font-size: 16px;
  font-weight: bold;
  color: #ff6b35;
  margin-bottom: 4px;
}

.product-stats {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #999;
}

.product-actions {
  display: flex;
  align-items: center;
}

.load-more {
  margin: 16px 0;
}

.empty-state {
  padding: 60px 20px;
}
</style>
