<template>
  <div class="settings-page">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="设置"
      left-text="返回"
      left-arrow
      @click-left="goBack"
    />

    <!-- 账号与安全 -->
    <div class="settings-section">
      <div class="section-title">账号与安全</div>
      <van-cell-group>
        <van-cell 
          title="账号安全" 
          icon="shield-o" 
          is-link 
          @click="goToAccountSecurity"
        />
        <van-cell 
          title="隐私设置" 
          icon="eye-o" 
          is-link 
          @click="goToPrivacySettings"
        />
        <van-cell 
          title="实名认证" 
          icon="certificate" 
          is-link 
          :value="userInfo?.verified ? '已认证' : '未认证'"
          @click="goToVerification"
        />
      </van-cell-group>
    </div>

    <!-- 消息通知 -->
    <div class="settings-section">
      <div class="section-title">消息通知</div>
      <van-cell-group>
        <van-cell title="订单消息">
          <template #right-icon>
            <van-switch v-model="notificationSettings.order" @change="saveNotificationSettings" />
          </template>
        </van-cell>
        <van-cell title="促销活动">
          <template #right-icon>
            <van-switch v-model="notificationSettings.promotion" @change="saveNotificationSettings" />
          </template>
        </van-cell>
        <van-cell title="系统通知">
          <template #right-icon>
            <van-switch v-model="notificationSettings.system" @change="saveNotificationSettings" />
          </template>
        </van-cell>
        <van-cell title="声音提醒">
          <template #right-icon>
            <van-switch v-model="notificationSettings.sound" @change="saveNotificationSettings" />
          </template>
        </van-cell>
      </van-cell-group>
    </div>

    <!-- 购物偏好 -->
    <div class="settings-section">
      <div class="section-title">购物偏好</div>
      <van-cell-group>
        <van-cell 
          title="默认收货地址" 
          icon="location-o" 
          is-link 
          :value="defaultAddress?.name || '未设置'"
          @click="goToAddresses"
        />
        <van-cell 
          title="货币单位" 
          is-link 
          :value="currency"
          @click="showCurrencyPicker = true"
        />
        <van-cell 
          title="语言设置" 
          is-link 
          :value="language"
          @click="showLanguagePicker = true"
        />
      </van-cell-group>
    </div>

    <!-- 应用设置 -->
    <div class="settings-section">
      <div class="section-title">应用设置</div>
      <van-cell-group>
        <van-cell title="自动登录">
          <template #right-icon>
            <van-switch v-model="appSettings.autoLogin" @change="saveAppSettings" />
          </template>
        </van-cell>
        <van-cell title="生物识别">
          <template #right-icon>
            <van-switch v-model="appSettings.biometric" @change="saveAppSettings" />
          </template>
        </van-cell>
        <van-cell title="数据同步">
          <template #right-icon>
            <van-switch v-model="appSettings.dataSync" @change="saveAppSettings" />
          </template>
        </van-cell>
        <van-cell 
          title="清除缓存" 
          is-link 
          :value="cacheSize"
          @click="showClearCacheDialog = true"
        />
      </van-cell-group>
    </div>

    <!-- 帮助与反馈 -->
    <div class="settings-section">
      <div class="section-title">帮助与反馈</div>
      <van-cell-group>
        <van-cell 
          title="帮助中心" 
          icon="question-o" 
          is-link 
          @click="goToHelp"
        />
        <van-cell 
          title="意见反馈" 
          icon="comment-o" 
          is-link 
          @click="goToFeedback"
        />
        <van-cell 
          title="联系客服" 
          icon="service" 
          is-link 
          @click="contactService"
        />
        <van-cell 
          title="关于我们" 
          icon="info-o" 
          is-link 
          @click="goToAbout"
        />
      </van-cell-group>
    </div>

    <!-- 版本信息 -->
    <div class="version-info">
      <div class="version-text">版本号：v1.0.0</div>
      <div class="copyright">© 2024 购物车项目</div>
    </div>

    <!-- 退出登录 -->
    <div class="logout-section">
      <van-button 
        type="danger" 
        block 
        plain 
        @click="showLogoutDialog = true"
      >
        退出登录
      </van-button>
    </div>

    <!-- 退出确认弹窗 -->
    <van-dialog
      v-model:show="showLogoutDialog"
      title="确认退出"
      message="确定要退出登录吗？"
      show-cancel-button
      @confirm="handleLogout"
    />

    <!-- 清除缓存确认弹窗 -->
    <van-dialog
      v-model:show="showClearCacheDialog"
      title="清除缓存"
      message="确定要清除应用缓存吗？这将删除所有本地数据。"
      show-cancel-button
      @confirm="clearCache"
    />

    <!-- 货币选择器 -->
    <van-popup v-model:show="showCurrencyPicker" position="bottom">
      <van-picker
        :columns="currencyOptions"
        @confirm="onCurrencyConfirm"
        @cancel="showCurrencyPicker = false"
      />
    </van-popup>

    <!-- 语言选择器 -->
    <van-popup v-model:show="showLanguagePicker" position="bottom">
      <van-picker
        :columns="languageOptions"
        @confirm="onLanguageConfirm"
        @cancel="showLanguagePicker = false"
      />
    </van-popup>
  </div>
</template>

<script lang="ts" setup name="SettingsPage">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useStore } from '@/store';
import { Toast } from 'vant';

const router = useRouter();
const store = useStore();

// 响应式数据
const showLogoutDialog = ref(false);
const showClearCacheDialog = ref(false);
const showCurrencyPicker = ref(false);
const showLanguagePicker = ref(false);

const notificationSettings = ref({
  order: true,
  promotion: false,
  system: true,
  sound: true
});

const appSettings = ref({
  autoLogin: true,
  biometric: false,
  dataSync: true
});

const currency = ref('人民币 ¥');
const language = ref('简体中文');
const cacheSize = ref('12.5MB');

// 选择器选项
const currencyOptions = [
  { text: '人民币 ¥', value: 'CNY' },
  { text: '美元 $', value: 'USD' },
  { text: '欧元 €', value: 'EUR' },
  { text: '日元 ¥', value: 'JPY' }
];

const languageOptions = [
  { text: '简体中文', value: 'zh-CN' },
  { text: '繁体中文', value: 'zh-TW' },
  { text: 'English', value: 'en-US' },
  { text: '日本語', value: 'ja-JP' }
];

// 计算属性
const userInfo = computed(() => store.user);
const defaultAddress = computed(() => store.defaultAddress);

// 方法
function goBack() {
  router.go(-1);
}

function goToAccountSecurity() {
  Toast('功能开发中...');
}

function goToPrivacySettings() {
  Toast('功能开发中...');
}

function goToVerification() {
  Toast('功能开发中...');
}

function goToAddresses() {
  router.push('/addresses');
}

function goToHelp() {
  Toast('功能开发中...');
}

function goToFeedback() {
  Toast('功能开发中...');
}

function contactService() {
  Toast('客服电话：400-123-4567');
}

function goToAbout() {
  Toast('关于我们页面开发中...');
}

function saveNotificationSettings() {
  localStorage.setItem('notificationSettings', JSON.stringify(notificationSettings.value));
  Toast.success('设置已保存');
}

function saveAppSettings() {
  localStorage.setItem('appSettings', JSON.stringify(appSettings.value));
  Toast.success('设置已保存');
}

function onCurrencyConfirm(option: any) {
  currency.value = option.text;
  localStorage.setItem('currency', option.value);
  showCurrencyPicker.value = false;
  Toast.success('货币设置已更新');
}

function onLanguageConfirm(option: any) {
  language.value = option.text;
  localStorage.setItem('language', option.value);
  showLanguagePicker.value = false;
  Toast.success('语言设置已更新');
}

function clearCache() {
  // 清除应用缓存
  localStorage.clear();
  sessionStorage.clear();
  
  // 重新初始化必要数据
  store.loadFromStorage();
  
  cacheSize.value = '0MB';
  Toast.success('缓存已清除');
  showClearCacheDialog.value = false;
}

function handleLogout() {
  store.logout();
  Toast.success('已退出登录');
  router.push('/login');
}

// 初始化设置
function initSettings() {
  try {
    // 加载通知设置
    const savedNotificationSettings = localStorage.getItem('notificationSettings');
    if (savedNotificationSettings) {
      notificationSettings.value = JSON.parse(savedNotificationSettings);
    }

    // 加载应用设置
    const savedAppSettings = localStorage.getItem('appSettings');
    if (savedAppSettings) {
      appSettings.value = JSON.parse(savedAppSettings);
    }

    // 加载货币设置
    const savedCurrency = localStorage.getItem('currency');
    if (savedCurrency) {
      const currencyOption = currencyOptions.find(opt => opt.value === savedCurrency);
      if (currencyOption) {
        currency.value = currencyOption.text;
      }
    }

    // 加载语言设置
    const savedLanguage = localStorage.getItem('language');
    if (savedLanguage) {
      const languageOption = languageOptions.find(opt => opt.value === savedLanguage);
      if (languageOption) {
        language.value = languageOption.text;
      }
    }
  } catch (error) {
    console.error('加载设置失败:', error);
  }
}

onMounted(() => {
  initSettings();
});
</script>

<style scoped>
.settings-page {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 20px;
}

.settings-section {
  margin: 10px 16px;
}

.section-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  padding-left: 4px;
}

.version-info {
  text-align: center;
  padding: 20px;
  color: #999;
  font-size: 12px;
}

.version-text {
  margin-bottom: 4px;
}

.copyright {
  font-size: 11px;
}

.logout-section {
  margin: 20px 16px;
}
</style>
