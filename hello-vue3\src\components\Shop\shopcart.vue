<template>
  <div class="empty" v-if="goodsNum ==0">
    <van-empty image="search" description="当前购物车为空" />
  </div>
  <div v-else>
    <div class="goodsTop"><h2 >购物车</h2></div>
    <!-- 商品购物车区域 -->
     <div class="goodsCenter">
        <div class="goodsItem" v-for="(item,index) in goodsData" :key="index">
            <div class="left">
                <img :src="item.img" alt="">
            </div>
            <div class="center">{{item.title}}</div>
            <div class="right"><van-stepper v-model="item.cartCount" theme="round" button-size="22" disable-input /></div>
        </div>
     <div class="goodsBottom">
        <van-button plain type="success" color="red" @click="clickButton(1)">下单</van-button>
        <van-button plain type="success" color="red" @click="clickButton(2)">清空</van-button>
    </div>
    </div>
  </div>
</template>

<script lang="ts" setup name="">
import { computed } from 'vue';
import { useStore } from '@/store';
import { useRouter } from 'vue-router';
import { Toast } from 'vant';
const store = useStore();
const router = useRouter();
// 购物车商品总数
const goodsNum = computed(() => store.cartItemsCount);
// 购物车商品数据
const goodsData = computed(() => store.cartItems);
//触发下单清空的函数
function clickButton(val: any) {
  if (val == 1) {
    // 检查购物车是否为空
    if (store.cartItems.length === 0) {
      Toast.fail("购物车为空，无法下单");
      return;
    }
    // 跳转到付款页面
    router.push('/payment');
  } else if (val == 2) {
    store.clearCart();
    Toast.success("购物车已清空");
  }
}

</script>
<style scoped>
.empty{
    margin-top: 5rem;
}
h2{
    text-align: center;
}
.goodsItem{
    position: relative;
    width: 100%;
    height: 5rem;
    margin-top: 1rem;
}
img{
    height: 100%;
}
.goodsCenter{
    position: relative;
    width: 100%;
    min-height: 5rem;
}
.left{
    height: 5rem;
    position: absolute;
    left: 1rem;
}
.center{
    position: absolute;
    top: 0.5rem;
    right: 1.5rem;
}
.right{
    position: absolute;
    bottom: 0.5rem;
    right: 1.5rem;
}
.goodsBottom{
    margin-top: 2rem;
    margin-bottom: 5rem;
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>
