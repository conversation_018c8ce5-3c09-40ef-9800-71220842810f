<template>
  <div class="favorites-container">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="我的收藏"
      left-text="返回"
      left-arrow
      @click-left="goBack"
    >
      <template #right>
        <span v-if="isEditMode" @click="toggleEditMode">完成</span>
        <span v-else @click="toggleEditMode">编辑</span>
      </template>
    </van-nav-bar>

    <!-- 空状态 -->
    <div v-if="favorites.length === 0" class="empty-state">
      <van-empty 
        image="search" 
        description="暂无收藏商品"
      >
        <van-button type="primary" @click="goToHome">
          去逛逛
        </van-button>
      </van-empty>
    </div>

    <!-- 收藏列表 -->
    <div v-else class="favorites-list">
      <div 
        v-for="item in favorites" 
        :key="item.id" 
        class="favorite-item"
        @click="goToProduct(item.product)"
      >
        <!-- 编辑模式下的选择框 -->
        <van-checkbox 
          v-if="isEditMode"
          v-model="selectedItems"
          :name="item.id"
          class="item-checkbox"
          @click.stop
        />
        
        <!-- 商品图片 -->
        <div class="item-image">
          <img :src="item.product.img" alt="商品图片" />
        </div>
        
        <!-- 商品信息 -->
        <div class="item-info">
          <div class="item-title">{{ item.product.title }}</div>
          <div class="item-description">{{ item.product.details }}</div>
          <div class="item-price">¥{{ item.product.price }}</div>
          <div class="item-time">{{ formatTime(item.createTime) }}</div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="item-actions" v-if="!isEditMode">
          <van-button 
            size="small" 
            type="primary" 
            @click.stop="addToCart(item.product)"
          >
            加购物车
          </van-button>
          <van-icon 
            name="delete-o" 
            class="delete-icon"
            @click.stop="removeFavorite(item.id)"
          />
        </div>
      </div>
    </div>

    <!-- 底部操作栏（编辑模式） -->
    <div v-if="isEditMode && favorites.length > 0" class="bottom-actions">
      <van-checkbox 
        v-model="selectAll"
        @change="handleSelectAll"
      >
        全选
      </van-checkbox>
      <div class="action-buttons">
        <van-button 
          type="primary" 
          size="small"
          :disabled="selectedItems.length === 0"
          @click="batchAddToCart"
        >
          加入购物车({{ selectedItems.length }})
        </van-button>
        <van-button 
          type="danger" 
          size="small"
          :disabled="selectedItems.length === 0"
          @click="showDeleteDialog = true"
        >
          删除({{ selectedItems.length }})
        </van-button>
      </div>
    </div>

    <!-- 删除确认弹窗 -->
    <van-dialog
      v-model:show="showDeleteDialog"
      title="确认删除"
      :message="`确定要删除选中的 ${selectedItems.length} 件商品吗？`"
      show-cancel-button
      @confirm="batchRemoveFavorites"
    />
  </div>
</template>

<script lang="ts" setup name="Favorites">
import { ref, computed, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useStore } from '@/store';
import { Toast } from 'vant';

const router = useRouter();
const store = useStore();

// 响应式数据
const isEditMode = ref(false);
const selectedItems = ref<number[]>([]);
const showDeleteDialog = ref(false);

// 计算属性
const favorites = computed(() => store.favorites);

const selectAll = computed({
  get: () => selectedItems.value.length === favorites.value.length && favorites.value.length > 0,
  set: (value: boolean) => {
    if (value) {
      selectedItems.value = favorites.value.map(item => item.id);
    } else {
      selectedItems.value = [];
    }
  }
});

// 监听编辑模式变化
watch(isEditMode, (newValue) => {
  if (!newValue) {
    selectedItems.value = [];
  }
});

// 方法
function goBack() {
  router.go(-1);
}

function goToHome() {
  router.push('/home/<USER>');
}

function goToProduct(product: any) {
  if (!isEditMode.value) {
    router.push(`/product/${product.id}`);
  }
}

function toggleEditMode() {
  isEditMode.value = !isEditMode.value;
}

function handleSelectAll(checked: boolean) {
  selectAll.value = checked;
}

function addToCart(product: any) {
  store.addToCart(product);
  Toast.success('已添加到购物车');
}

function removeFavorite(favoriteId: number) {
  const favorite = favorites.value.find(item => item.id === favoriteId);
  if (favorite) {
    store.removeFromFavorites(favorite.productId);
    Toast.success('已取消收藏');
  }
}

function batchAddToCart() {
  let addedCount = 0;
  selectedItems.value.forEach(favoriteId => {
    const favorite = favorites.value.find(item => item.id === favoriteId);
    if (favorite) {
      store.addToCart(favorite.product);
      addedCount++;
    }
  });
  
  if (addedCount > 0) {
    Toast.success(`已添加 ${addedCount} 件商品到购物车`);
    selectedItems.value = [];
    isEditMode.value = false;
  }
}

function batchRemoveFavorites() {
  let removedCount = 0;
  selectedItems.value.forEach(favoriteId => {
    const favorite = favorites.value.find(item => item.id === favoriteId);
    if (favorite) {
      store.removeFromFavorites(favorite.productId);
      removedCount++;
    }
  });
  
  if (removedCount > 0) {
    Toast.success(`已删除 ${removedCount} 件商品`);
    selectedItems.value = [];
    isEditMode.value = false;
  }
  
  showDeleteDialog.value = false;
}

function formatTime(timeStr: string) {
  const date = new Date(timeStr);
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  
  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  
  if (minutes < 60) {
    return `${minutes}分钟前`;
  } else if (hours < 24) {
    return `${hours}小时前`;
  } else if (days < 30) {
    return `${days}天前`;
  } else {
    return date.toLocaleDateString();
  }
}
</script>

<style scoped>
.favorites-container {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 80px;
}

.empty-state {
  padding: 60px 20px;
}

.favorites-list {
  padding: 0 16px;
}

.favorite-item {
  display: flex;
  align-items: center;
  background: white;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.favorite-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.item-checkbox {
  margin-right: 12px;
}

.item-image {
  width: 80px;
  height: 80px;
  margin-right: 12px;
  flex-shrink: 0;
}

.item-image img {
  width: 100%;
  height: 100%;
  border-radius: 6px;
  object-fit: cover;
}

.item-info {
  flex: 1;
  min-width: 0;
}

.item-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-description {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-price {
  font-size: 16px;
  font-weight: bold;
  color: #ff6b35;
  margin-bottom: 4px;
}

.item-time {
  font-size: 12px;
  color: #999;
}

.item-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.delete-icon {
  font-size: 18px;
  color: #999;
  cursor: pointer;
  padding: 4px;
}

.delete-icon:hover {
  color: #ff6b35;
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 12px 16px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.action-buttons {
  display: flex;
  gap: 8px;
}
</style>
