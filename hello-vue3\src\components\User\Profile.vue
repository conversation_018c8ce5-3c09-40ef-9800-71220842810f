<template>
  <div class="profile-container">
    <!-- 用户信息头部 -->
    <div class="user-header">
      <div class="user-info">
        <img 
          :src="userInfo?.avatar || '/default-avatar.png'" 
          class="user-avatar"
          @click="changeAvatar"
        />
        <div class="user-details">
          <div class="user-name">{{ userInfo?.name || '未登录' }}</div>
          <div class="user-phone">{{ userInfo?.phone || '未绑定手机号' }}</div>
        </div>
        <van-icon name="arrow" class="arrow-icon" @click="editProfile" />
      </div>
    </div>

    <!-- 订单统计 -->
    <div class="order-stats">
      <div class="stats-title">我的订单</div>
      <div class="stats-grid">
        <div class="stats-item" @click="goToOrders('pending')">
          <div class="stats-count">{{ pendingOrdersCount }}</div>
          <div class="stats-label">待付款</div>
        </div>
        <div class="stats-item" @click="goToOrders('paid')">
          <div class="stats-count">{{ paidOrdersCount }}</div>
          <div class="stats-label">待发货</div>
        </div>
        <div class="stats-item" @click="goToOrders('shipped')">
          <div class="stats-count">{{ shippedOrdersCount }}</div>
          <div class="stats-label">待收货</div>
        </div>
        <div class="stats-item" @click="goToOrders('completed')">
          <div class="stats-count">{{ completedOrdersCount }}</div>
          <div class="stats-label">已完成</div>
        </div>
      </div>
      <div class="view-all-orders" @click="goToOrders()">
        查看全部订单
        <van-icon name="arrow" />
      </div>
    </div>

    <!-- 功能菜单 -->
    <div class="menu-section">
      <van-cell-group>
        <van-cell 
          title="收货地址" 
          icon="location-o" 
          is-link 
          @click="goToAddresses"
        />
        <van-cell 
          title="我的收藏" 
          icon="star-o" 
          is-link 
          :value="favoritesCount + '件'"
          @click="goToFavorites"
        />
        <van-cell 
          title="优惠券" 
          icon="coupon-o" 
          is-link 
          value="0张"
        />
        <van-cell 
          title="积分商城" 
          icon="gift-o" 
          is-link 
        />
      </van-cell-group>
    </div>

    <!-- 设置菜单 -->
    <div class="menu-section">
      <van-cell-group>
        <van-cell 
          title="账号安全" 
          icon="shield-o" 
          is-link 
        />
        <van-cell 
          title="消息通知" 
          icon="bell-o" 
          is-link 
        />
        <van-cell 
          title="隐私设置" 
          icon="eye-o" 
          is-link 
        />
        <van-cell 
          title="帮助中心" 
          icon="question-o" 
          is-link 
        />
        <van-cell 
          title="关于我们" 
          icon="info-o" 
          is-link 
        />
      </van-cell-group>
    </div>

    <!-- 退出登录 -->
    <div class="logout-section">
      <van-button 
        type="danger" 
        block 
        plain 
        @click="showLogoutDialog = true"
      >
        退出登录
      </van-button>
    </div>

    <!-- 退出确认弹窗 -->
    <van-dialog
      v-model:show="showLogoutDialog"
      title="确认退出"
      message="确定要退出登录吗？"
      show-cancel-button
      @confirm="handleLogout"
    />

    <!-- 头像选择弹窗 -->
    <van-popup v-model:show="showAvatarPicker" position="bottom" round>
      <div class="avatar-picker">
        <div class="picker-header">
          <h3>选择头像</h3>
          <van-icon name="cross" @click="showAvatarPicker = false" />
        </div>
        <div class="avatar-options">
          <div 
            v-for="(avatar, index) in avatarOptions" 
            :key="index"
            class="avatar-option"
            @click="selectAvatar(avatar)"
          >
            <img :src="avatar" alt="头像选项" />
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 个人信息编辑弹窗 -->
    <van-popup v-model:show="showEditProfile" position="bottom" round>
      <div class="edit-profile">
        <div class="edit-header">
          <h3>编辑个人信息</h3>
          <van-icon name="cross" @click="showEditProfile = false" />
        </div>
        <van-form @submit="saveProfile">
          <van-cell-group>
            <van-field
              v-model="editForm.name"
              name="name"
              label="昵称"
              placeholder="请输入昵称"
              :rules="[{ required: true, message: '请输入昵称' }]"
            />
            <van-field
              v-model="editForm.phone"
              name="phone"
              label="手机号"
              placeholder="请输入手机号"
              type="tel"
            />
            <van-field
              v-model="editForm.email"
              name="email"
              label="邮箱"
              placeholder="请输入邮箱"
              type="email"
            />
            <van-field name="gender" label="性别">
              <template #input>
                <van-radio-group v-model="editForm.gender" direction="horizontal">
                  <van-radio name="male">男</van-radio>
                  <van-radio name="female">女</van-radio>
                  <van-radio name="unknown">保密</van-radio>
                </van-radio-group>
              </template>
            </van-field>
            <van-field
              v-model="editForm.birthday"
              name="birthday"
              label="生日"
              placeholder="请选择生日"
              readonly
              @click="showDatePicker = true"
            />
          </van-cell-group>
          <div class="form-actions">
            <van-button type="primary" native-type="submit" block>
              保存
            </van-button>
          </div>
        </van-form>
      </div>
    </van-popup>

    <!-- 日期选择器 -->
    <van-popup v-model:show="showDatePicker" position="bottom">
      <van-date-picker
        v-model="selectedDate"
        title="选择生日"
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="confirmDate"
        @cancel="showDatePicker = false"
      />
    </van-popup>
  </div>
</template>

<script lang="ts" setup name="Profile">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useStore } from '@/store';
import { Toast, Dialog } from 'vant';
import type { User } from '@/types';

const router = useRouter();
const store = useStore();

// 响应式数据
const showLogoutDialog = ref(false);
const showAvatarPicker = ref(false);
const showEditProfile = ref(false);
const showDatePicker = ref(false);

const editForm = ref({
  name: '',
  phone: '',
  email: '',
  gender: 'unknown' as 'male' | 'female' | 'unknown',
  birthday: ''
});

const selectedDate = ref(new Date());
const minDate = ref(new Date(1950, 0, 1));
const maxDate = ref(new Date());

// 头像选项
const avatarOptions = ref([
  'https://img.yzcdn.cn/vant/cat.jpeg',
  'https://img.yzcdn.cn/vant/tree.jpg',
  'https://img.yzcdn.cn/vant/leaf.jpg',
  'https://img.yzcdn.cn/vant/sand.jpeg',
  'https://img.yzcdn.cn/vant/water.jpeg',
  'https://img.yzcdn.cn/vant/grass.jpeg'
]);

// 计算属性
const userInfo = computed(() => store.user);

const pendingOrdersCount = computed(() => 
  store.ordersByStatus('pending').length
);

const paidOrdersCount = computed(() => 
  store.ordersByStatus('paid').length
);

const shippedOrdersCount = computed(() => 
  store.ordersByStatus('shipped').length
);

const completedOrdersCount = computed(() => 
  store.ordersByStatus('completed').length
);

const favoritesCount = computed(() => store.favoritesCount);

// 方法
function editProfile() {
  if (userInfo.value) {
    editForm.value = {
      name: userInfo.value.name,
      phone: userInfo.value.phone || '',
      email: userInfo.value.email || '',
      gender: userInfo.value.gender || 'unknown',
      birthday: userInfo.value.birthday || ''
    };
  }
  showEditProfile.value = true;
}

function changeAvatar() {
  showAvatarPicker.value = true;
}

function selectAvatar(avatar: string) {
  if (userInfo.value) {
    const updatedUser: User = {
      ...userInfo.value,
      avatar,
      updateTime: new Date().toISOString()
    };
    store.setUser(updatedUser);
    Toast.success('头像更新成功');
  }
  showAvatarPicker.value = false;
}

function saveProfile() {
  if (userInfo.value) {
    const updatedUser: User = {
      ...userInfo.value,
      ...editForm.value,
      updateTime: new Date().toISOString()
    };
    store.setUser(updatedUser);
    Toast.success('个人信息更新成功');
    showEditProfile.value = false;
  }
}

function confirmDate() {
  editForm.value.birthday = selectedDate.value.toISOString().split('T')[0];
  showDatePicker.value = false;
}

function goToOrders(status?: string) {
  if (status) {
    router.push(`/orders?status=${status}`);
  } else {
    router.push('/orders');
  }
}

function goToAddresses() {
  router.push('/addresses');
}

function goToFavorites() {
  router.push('/favorites');
}

function handleLogout() {
  store.logout();
  Toast.success('已退出登录');
  router.push('/login');
}

// 初始化用户信息
function initUserInfo() {
  if (!userInfo.value) {
    // 如果没有用户信息，创建一个默认用户
    const defaultUser: User = {
      id: 1,
      name: 'admin',
      email: '',
      phone: '',
      avatar: '',
      gender: 'unknown',
      birthday: '',
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    };
    store.setUser(defaultUser);
  }
}

onMounted(() => {
  initUserInfo();
});
</script>

<style scoped>
.profile-container {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 20px;
}

.user-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px 16px;
  color: white;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  margin-right: 16px;
  cursor: pointer;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 4px;
}

.user-phone {
  font-size: 14px;
  opacity: 0.8;
}

.arrow-icon {
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
}

.order-stats {
  background: white;
  margin: 10px 16px;
  border-radius: 8px;
  padding: 16px;
}

.stats-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-bottom: 16px;
}

.stats-item {
  text-align: center;
  cursor: pointer;
}

.stats-count {
  font-size: 20px;
  font-weight: bold;
  color: #ff6b35;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 12px;
  color: #666;
}

.view-all-orders {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4px;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  padding: 8px 0;
  border-top: 1px solid #f5f5f5;
}

.menu-section {
  margin: 10px 16px;
  border-radius: 8px;
  overflow: hidden;
}

.logout-section {
  margin: 20px 16px;
}

/* 弹窗样式 */
.avatar-picker,
.edit-profile {
  padding: 20px;
}

.picker-header,
.edit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.avatar-options {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.avatar-option {
  cursor: pointer;
}

.avatar-option img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
}

.form-actions {
  margin-top: 20px;
}
</style>
