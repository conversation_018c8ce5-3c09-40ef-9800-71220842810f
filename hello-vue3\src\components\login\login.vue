<template>
  <div class="login-bg">
    <div class="login-card">
      <div class="login-deco"></div>
      <h2 class="login-title">Log In</h2>
      <van-form @submit="loginHandler">
        <van-field
          v-model="user.name"
          name="username"
          placeholder="User name / Email"
          :rules="[{ required: true, message: 'Please enter your username or email' }]"
        >
          <template #left-icon>
            <van-icon name="user-o" />
          </template>
        </van-field>
        <van-field
          v-model="user.password"
          name="password"
          type="password"
          placeholder="Password"
          :rules="[{ required: true, message: 'Please enter your password' }]"
        >
          <template #left-icon>
            <van-icon name="lock" />
          </template>
        </van-field>
        <div class="login-btn-wrap">
          <van-button
            round
            block
            type="primary"
            class="login-btn"
            native-type="submit"
            :loading="loading"
          >
            LOG IN NOW
            <van-icon name="arrow" class="arrow-icon" />
          </van-button>
        </div>
      </van-form>
      <div class="login-social">
        <span>log in via</span>
        <van-icon name="wechat" class="social-icon" />
        <van-icon name="qq" class="social-icon" />
        <van-icon name="weibo" class="social-icon" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup name="login">
import { ref, reactive } from 'vue';
import axios from '../../axios/axiosConfig';
import { useRouter } from 'vue-router';
import { Toast } from 'vant';

const router = useRouter();
const loading = ref(false);

const user = reactive({
  name: '',
  password: ''
});

function loginHandler() {
  if (loading.value) return;
  loading.value = true;
  axios.post('/api/login', {
    name: user.name,
    password: user.password
  })
.then((res) => {
      if (res.data.code === 200) {
        window.sessionStorage.setItem('token', res.data.data.token);
        user.password = '';
        router.push('/home');
      } else {
        Toast(res.data.message || 'Login failed');
      }
    })
    .catch((err) => {
      Toast('Request failed: ' + err);
    })
    .finally(() => {
      loading.value = false;
    });
}
</script>

<style scoped>
.login-bg {
  min-height: 100vh;
  background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}
.login-card {
  position: relative;
  width: 340px;
  padding: 48px 32px 32px 32px;
  background: #fff;
  border-radius: 20px;
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.2);
  display: flex;
  flex-direction: column;
  align-items: center;
}
.login-deco {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 110px;
  background: linear-gradient(120deg, #a18cd1 60%, #fbc2eb 100%);
  border-radius: 20px 20px 80px 0;
  z-index: 1;
}
.login-title {
  position: relative;
  z-index: 2;
  margin: 0 0 32px 0;
  font-size: 2rem;
  color: #5f4b8b;
  font-weight: 700;
  letter-spacing: 2px;
}
.van-form {
  width: 100%;
  z-index: 2;
}
.van-field {
  margin-bottom: 18px;
  background: #f7f7fa;
  border-radius: 8px;
}
.login-btn-wrap {
  margin-top: 10px;
}
.login-btn {
  background: linear-gradient(90deg, #a18cd1 0%, #fbc2eb 100%);
  color: #5f4b8b;
  font-weight: 600;
  font-size: 1rem;
  border: none;
  box-shadow: 0 2px 8px rgba(161, 140, 209, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
}
.arrow-icon {
  margin-left: 8px;
  font-size: 18px;
}
.login-social {
  margin-top: 32px;
  text-align: center;
  color: #b3a1d1;
  font-size: 0.95rem;
  z-index: 2;
}
.social-icon {
  margin: 0 8px;
  font-size: 22px;
  vertical-align: middle;
}
</style>