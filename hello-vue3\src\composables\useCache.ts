/**
 * 缓存管理组合式函数
 * 提供内存缓存、本地存储缓存、组件缓存等功能
 */

import { ref, computed, watch } from 'vue';

// 内存缓存接口
interface CacheItem<T> {
  data: T;
  timestamp: number;
  expiry?: number; // 过期时间（毫秒）
}

// 缓存配置
interface CacheOptions {
  maxSize?: number; // 最大缓存数量
  defaultExpiry?: number; // 默认过期时间（毫秒）
  storage?: 'memory' | 'localStorage' | 'sessionStorage';
}

/**
 * 内存缓存
 */
export function useMemoryCache<T = any>(options: CacheOptions = {}) {
  const {
    maxSize = 100,
    defaultExpiry = 5 * 60 * 1000, // 5分钟
    storage = 'memory'
  } = options;

  const cache = ref<Map<string, CacheItem<T>>>(new Map());

  // 设置缓存
  const set = (key: string, data: T, expiry?: number) => {
    const item: CacheItem<T> = {
      data,
      timestamp: Date.now(),
      expiry: expiry || defaultExpiry
    };

    // 如果超过最大缓存数量，删除最旧的项
    if (cache.value.size >= maxSize) {
      const firstKey = cache.value.keys().next().value;
      if (firstKey) {
        cache.value.delete(firstKey);
      }
    }

    cache.value.set(key, item);

    // 如果使用本地存储，同步到存储
    if (storage !== 'memory') {
      syncToStorage();
    }
  };

  // 获取缓存
  const get = (key: string): T | null => {
    const item = cache.value.get(key);
    
    if (!item) {
      return null;
    }

    // 检查是否过期
    if (item.expiry && Date.now() - item.timestamp > item.expiry) {
      cache.value.delete(key);
      if (storage !== 'memory') {
        syncToStorage();
      }
      return null;
    }

    return item.data;
  };

  // 删除缓存
  const remove = (key: string) => {
    const result = cache.value.delete(key);
    if (storage !== 'memory') {
      syncToStorage();
    }
    return result;
  };

  // 清空缓存
  const clear = () => {
    cache.value.clear();
    if (storage !== 'memory') {
      syncToStorage();
    }
  };

  // 检查是否存在
  const has = (key: string): boolean => {
    const item = cache.value.get(key);
    if (!item) return false;

    // 检查是否过期
    if (item.expiry && Date.now() - item.timestamp > item.expiry) {
      cache.value.delete(key);
      if (storage !== 'memory') {
        syncToStorage();
      }
      return false;
    }

    return true;
  };

  // 获取所有键
  const keys = computed(() => Array.from(cache.value.keys()));

  // 获取缓存大小
  const size = computed(() => cache.value.size);

  // 同步到本地存储
  const syncToStorage = () => {
    if (storage === 'memory') return;

    try {
      const data = Array.from(cache.value.entries());
      const storageApi = storage === 'localStorage' ? localStorage : sessionStorage;
      storageApi.setItem('cache-data', JSON.stringify(data));
    } catch (error) {
      console.error('同步缓存到存储失败:', error);
    }
  };

  // 从本地存储加载
  const loadFromStorage = () => {
    if (storage === 'memory') return;

    try {
      const storageApi = storage === 'localStorage' ? localStorage : sessionStorage;
      const data = storageApi.getItem('cache-data');
      
      if (data) {
        const entries = JSON.parse(data);
        cache.value = new Map(entries);
      }
    } catch (error) {
      console.error('从存储加载缓存失败:', error);
    }
  };

  // 清理过期缓存
  const cleanup = () => {
    const now = Date.now();
    const keysToDelete: string[] = [];

    cache.value.forEach((item, key) => {
      if (item.expiry && now - item.timestamp > item.expiry) {
        keysToDelete.push(key);
      }
    });

    keysToDelete.forEach(key => cache.value.delete(key));

    if (storage !== 'memory' && keysToDelete.length > 0) {
      syncToStorage();
    }

    return keysToDelete.length;
  };

  // 初始化时从存储加载
  if (storage !== 'memory') {
    loadFromStorage();
  }

  return {
    cache: cache.value,
    set,
    get,
    remove,
    clear,
    has,
    keys,
    size,
    cleanup,
    syncToStorage,
    loadFromStorage
  };
}

/**
 * API响应缓存
 */
export function useApiCache(options: CacheOptions = {}) {
  const cache = useMemoryCache(options);

  // 生成缓存键
  const generateKey = (url: string, params?: any): string => {
    const paramStr = params ? JSON.stringify(params) : '';
    return `${url}:${paramStr}`;
  };

  // 缓存API响应
  const cacheResponse = (url: string, params: any, response: any, expiry?: number) => {
    const key = generateKey(url, params);
    cache.set(key, response, expiry);
  };

  // 获取缓存的响应
  const getCachedResponse = (url: string, params?: any) => {
    const key = generateKey(url, params);
    return cache.get(key);
  };

  // 删除API缓存
  const removeCachedResponse = (url: string, params?: any) => {
    const key = generateKey(url, params);
    return cache.remove(key);
  };

  return {
    ...cache,
    generateKey,
    cacheResponse,
    getCachedResponse,
    removeCachedResponse
  };
}

/**
 * 图片缓存
 */
export function useImageCache() {
  const cache = useMemoryCache<string>({
    maxSize: 200,
    defaultExpiry: 30 * 60 * 1000, // 30分钟
    storage: 'localStorage'
  });

  // 预加载图片
  const preloadImage = (url: string): Promise<string> => {
    return new Promise((resolve, reject) => {
      // 检查缓存
      const cached = cache.get(url);
      if (cached) {
        resolve(cached);
        return;
      }

      const img = new Image();
      img.onload = () => {
        cache.set(url, url);
        resolve(url);
      };
      img.onerror = () => {
        reject(new Error(`图片加载失败: ${url}`));
      };
      img.src = url;
    });
  };

  // 批量预加载图片
  const preloadImages = async (urls: string[]): Promise<string[]> => {
    const promises = urls.map(url => preloadImage(url).catch(() => null));
    const results = await Promise.all(promises);
    return results.filter(Boolean) as string[];
  };

  return {
    ...cache,
    preloadImage,
    preloadImages
  };
}

/**
 * 组件状态缓存
 */
export function useComponentCache<T = any>(componentName: string) {
  const cache = useMemoryCache<T>({
    storage: 'sessionStorage',
    defaultExpiry: 60 * 60 * 1000 // 1小时
  });

  // 保存组件状态
  const saveState = (state: T) => {
    cache.set(componentName, state);
  };

  // 恢复组件状态
  const restoreState = (): T | null => {
    return cache.get(componentName);
  };

  // 清除组件状态
  const clearState = () => {
    cache.remove(componentName);
  };

  return {
    saveState,
    restoreState,
    clearState
  };
}

/**
 * 路由缓存管理
 */
export function useRouteCache() {
  const routeCache = ref<Set<string>>(new Set());

  // 添加到缓存
  const addToCache = (routeName: string) => {
    routeCache.value.add(routeName);
  };

  // 从缓存移除
  const removeFromCache = (routeName: string) => {
    routeCache.value.delete(routeName);
  };

  // 清空缓存
  const clearCache = () => {
    routeCache.value.clear();
  };

  // 检查是否在缓存中
  const isInCache = (routeName: string) => {
    return routeCache.value.has(routeName);
  };

  // 获取缓存的路由列表
  const cachedRoutes = computed(() => Array.from(routeCache.value));

  return {
    routeCache,
    addToCache,
    removeFromCache,
    clearCache,
    isInCache,
    cachedRoutes
  };
}

// 全局缓存清理
export function setupGlobalCacheCleanup() {
  // 定期清理过期缓存
  const cleanupInterval = setInterval(() => {
    // 这里可以添加全局缓存清理逻辑
    console.log('执行全局缓存清理...');
  }, 10 * 60 * 1000); // 每10分钟清理一次

  // 页面卸载时清理
  const cleanup = () => {
    clearInterval(cleanupInterval);
  };

  // 监听页面卸载
  if (typeof window !== 'undefined') {
    window.addEventListener('beforeunload', cleanup);
  }

  return cleanup;
}
