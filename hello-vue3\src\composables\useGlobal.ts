/**
 * 全局状态管理组合式函数
 * 包括加载状态、错误处理、网络状态等
 */

import { ref, computed } from 'vue';
import { Toast } from 'vant';
import type { LoadingState, ErrorInfo, NetworkState } from '@/types';

// 全局状态
const globalLoading = ref<LoadingState>({
  global: false
});

const globalError = ref<ErrorInfo | null>(null);

const networkState = ref<NetworkState>({
  online: navigator.onLine,
  effectiveType: (navigator as any).connection?.effectiveType || 'unknown'
});

/**
 * 加载状态管理
 */
export function useLoading() {
  // 设置全局加载状态
  const setGlobalLoading = (loading: boolean) => {
    globalLoading.value.global = loading;
  };

  // 设置特定模块的加载状态
  const setModuleLoading = (module: string, loading: boolean) => {
    globalLoading.value[module] = loading;
  };

  // 获取全局加载状态
  const isGlobalLoading = computed(() => globalLoading.value.global);

  // 获取特定模块的加载状态
  const isModuleLoading = (module: string) => {
    return computed(() => globalLoading.value[module] || false);
  };

  // 异步操作包装器，自动管理加载状态
  const withLoading = async <T>(
    asyncFn: () => Promise<T>,
    options: {
      global?: boolean;
      module?: string;
      showToast?: boolean;
      errorMessage?: string;
    } = {}
  ): Promise<T | null> => {
    const { global = false, module, showToast = true, errorMessage = '操作失败' } = options;

    try {
      // 设置加载状态
      if (global) {
        setGlobalLoading(true);
      } else if (module) {
        setModuleLoading(module, true);
      }

      const result = await asyncFn();
      return result;
    } catch (error) {
      console.error('异步操作失败:', error);
      
      // 设置错误信息
      setError({
        message: error instanceof Error ? error.message : errorMessage,
        timestamp: new Date().toISOString()
      });

      if (showToast) {
        Toast.fail(errorMessage);
      }

      return null;
    } finally {
      // 清除加载状态
      if (global) {
        setGlobalLoading(false);
      } else if (module) {
        setModuleLoading(module, false);
      }
    }
  };

  return {
    globalLoading: globalLoading.value,
    isGlobalLoading,
    isModuleLoading,
    setGlobalLoading,
    setModuleLoading,
    withLoading
  };
}

/**
 * 错误处理
 */
export function useError() {
  // 设置错误信息
  const setError = (error: ErrorInfo) => {
    globalError.value = error;
  };

  // 清除错误信息
  const clearError = () => {
    globalError.value = null;
  };

  // 获取当前错误
  const currentError = computed(() => globalError.value);

  // 错误处理器
  const handleError = (
    error: unknown,
    options: {
      showToast?: boolean;
      customMessage?: string;
      logToConsole?: boolean;
    } = {}
  ) => {
    const { showToast = true, customMessage, logToConsole = true } = options;

    let errorMessage = '未知错误';
    let errorCode: number | undefined;

    if (error instanceof Error) {
      errorMessage = error.message;
    } else if (typeof error === 'string') {
      errorMessage = error;
    } else if (error && typeof error === 'object' && 'message' in error) {
      errorMessage = (error as any).message;
      errorCode = (error as any).code;
    }

    const finalMessage = customMessage || errorMessage;

    // 设置错误状态
    setError({
      code: errorCode,
      message: finalMessage,
      timestamp: new Date().toISOString()
    });

    // 显示错误提示
    if (showToast) {
      Toast.fail(finalMessage);
    }

    // 控制台输出
    if (logToConsole) {
      console.error('错误处理:', error);
    }
  };

  // API错误处理
  const handleApiError = (response: any) => {
    if (response?.code !== 200 && response?.code !== 0) {
      const errorMessage = response?.message || 'API请求失败';
      handleError(new Error(errorMessage), {
        customMessage: errorMessage
      });
      return false;
    }
    return true;
  };

  return {
    currentError,
    setError,
    clearError,
    handleError,
    handleApiError
  };
}

/**
 * 网络状态管理
 */
export function useNetwork() {
  // 更新网络状态
  const updateNetworkState = () => {
    networkState.value.online = navigator.onLine;
    networkState.value.effectiveType = (navigator as any).connection?.effectiveType || 'unknown';
  };

  // 监听网络状态变化
  const setupNetworkListeners = () => {
    window.addEventListener('online', () => {
      updateNetworkState();
      Toast.success('网络已连接');
    });

    window.addEventListener('offline', () => {
      updateNetworkState();
      Toast.fail('网络已断开');
    });

    // 监听网络类型变化
    if ((navigator as any).connection) {
      (navigator as any).connection.addEventListener('change', updateNetworkState);
    }
  };

  // 检查网络状态
  const checkNetworkStatus = () => {
    if (!networkState.value.online) {
      Toast.fail('网络连接不可用，请检查网络设置');
      return false;
    }
    return true;
  };

  // 获取网络状态
  const isOnline = computed(() => networkState.value.online);
  const networkType = computed(() => networkState.value.effectiveType);

  return {
    networkState: networkState.value,
    isOnline,
    networkType,
    updateNetworkState,
    setupNetworkListeners,
    checkNetworkStatus
  };
}

/**
 * 重试机制
 */
export function useRetry() {
  const retry = async <T>(
    asyncFn: () => Promise<T>,
    options: {
      maxAttempts?: number;
      delay?: number;
      backoff?: boolean;
      onRetry?: (attempt: number, error: any) => void;
    } = {}
  ): Promise<T> => {
    const { 
      maxAttempts = 3, 
      delay = 1000, 
      backoff = true,
      onRetry 
    } = options;

    let lastError: any;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await asyncFn();
      } catch (error) {
        lastError = error;
        
        if (attempt === maxAttempts) {
          throw error;
        }

        // 调用重试回调
        if (onRetry) {
          onRetry(attempt, error);
        }

        // 计算延迟时间
        const currentDelay = backoff ? delay * Math.pow(2, attempt - 1) : delay;
        
        // 等待后重试
        await new Promise(resolve => setTimeout(resolve, currentDelay));
      }
    }

    throw lastError;
  };

  return {
    retry
  };
}

/**
 * 防抖和节流
 */
export function useThrottle() {
  // 防抖
  const debounce = <T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): ((...args: Parameters<T>) => void) => {
    let timeoutId: NodeJS.Timeout;
    
    return (...args: Parameters<T>) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func(...args), delay);
    };
  };

  // 节流
  const throttle = <T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): ((...args: Parameters<T>) => void) => {
    let lastCall = 0;
    
    return (...args: Parameters<T>) => {
      const now = Date.now();
      if (now - lastCall >= delay) {
        lastCall = now;
        func(...args);
      }
    };
  };

  return {
    debounce,
    throttle
  };
}

/**
 * 全局状态初始化
 */
export function initGlobalState() {
  const { setupNetworkListeners } = useNetwork();
  
  // 设置网络监听器
  setupNetworkListeners();
  
  // 可以在这里添加其他全局初始化逻辑
}

// 导出全局状态（用于调试）
export const globalState = {
  loading: globalLoading,
  error: globalError,
  network: networkState
};
