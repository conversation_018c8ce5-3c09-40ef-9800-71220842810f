// 导入Vue的createApp函数，用于创建Vue应用实例
import { createApp } from 'vue'
// 导入根组件App.vue
import App from './App.vue'
// 导入路由配置
import router from './router'
// 导入Vant UI组件库
import vant from 'vant'
// 导入Vant的CSS样式
import 'vant/lib/index.css'
// 导入自定义的axios实例，用于HTTP请求
import http from './axios/axiosConfig'
// 导入Pinia状态管理库的创建函数
import { createPinia } from 'pinia'

// 创建Pinia实例，用于状态管理
const pinia = createPinia()

// 创建Vue应用实例，以App.vue作为根组件
const app = createApp(App)

// 将axios实例挂载到全局属性$http上，便于在组件中通过this.$http访问
app.config.globalProperties.$http = http;

// 安装路由、Vant组件库和Pinia插件
app.use(router).use(vant).use(pinia)

// 初始化store数据
import { useStore } from './store'
const store = useStore()
store.loadFromStorage()

// 初始化全局状态
import { initGlobalState } from './composables/useGlobal'
initGlobalState()

// 设置全局缓存清理
import { setupGlobalCacheCleanup } from './composables/useCache'
setupGlobalCacheCleanup()

// 挂载应用
app.mount('#app')

// 在开发环境下导入mock数据模块，用于模拟API响应
if (import.meta.env.MODE === 'development') {
  import('./mock');
}