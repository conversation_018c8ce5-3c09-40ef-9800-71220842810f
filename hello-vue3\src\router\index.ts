import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

// 移除所有静态导入（如 import Home from ...）
// 改为使用懒加载定义组件

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Root',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('../components/login/login.vue') // 懒加载登录组件
  },
  {
    path: '/home',
    name: 'Home',
    component: () => import('@/components/Home/home.vue'), // 懒加载主容器
    redirect: '/home/<USER>',
    children: [
      {
        path: 'homeTop',
        name: 'HomeTop',
        component: () => import('@/components/Home/components/top.vue') // 懒加载首页内容
      },
      {
        path: 'shopCart',
        name: 'ShopCart',
        component: () => import('@/components/Shop/shopcart.vue') // 懒加载购物车
      },
      {
        path: 'profile',
        name: 'Profile',
        component: () => import('@/components/User/Profile.vue') // 懒加载个人中心
      },
      {
        path: 'settings',
        name: 'Settings',
        component: () => import('@/components/Settings/SettingsPage.vue') // 懒加载设置页面
      }
    ]
  },
  {
    path: '/payment',
    name: 'Payment',
    component: () => import('@/components/Payment/payment.vue') // 懒加载付款页面
  },
  {
    path: '/product/:id',
    name: 'ProductDetail',
    component: () => import('@/components/Product/ProductDetail.vue') // 懒加载商品详情页面
  },
  {
    path: '/favorites',
    name: 'Favorites',
    component: () => import('@/components/User/Favorites.vue') // 懒加载收藏页面
  },
  {
    path: '/search',
    name: 'Search',
    component: () => import('@/components/Search/SearchPage.vue') // 懒加载搜索页面
  },
  {
    path: '/orders',
    name: 'OrderList',
    component: () => import('@/components/Order/OrderList.vue') // 懒加载订单列表页面
  },
  {
    path: '/addresses',
    name: 'AddressList',
    component: () => import('@/components/Address/AddressList.vue') // 懒加载地址管理页面
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

/**
 * 全局前置导航守卫
 * - 拦截所有路由跳转请求
 * - 实现登录状态验证
 */
router.beforeEach((to, _from, next) => {
  // 从sessionStorage获取登录令牌
  const token = window.sessionStorage.getItem('token');
  
  // 检查是否需要登录权限
  if (to.path !== '/login' && !token) {
    // 未登录且访问需要权限的页面，强制跳转登录页
    next('/login');
  } else {
    // 已登录或访问登录页，放行
    next();
  }
});

export default router