import { defineStore } from 'pinia'
import type {
  User,
  CartItem,
  Order,
  Address,
  FavoriteItem,
  LoadingState,
  ErrorInfo
} from '@/types'

/**
 * 主应用状态管理
 * - 用户信息管理
 * - 购物车管理
 * - 订单管理
 * - 收藏管理
 * - 全局状态管理
 */
export const useStore = defineStore('main', {
  state: () => ({
    // 用户相关
    user: null as User | null,
    token: '',

    // 购物车相关
    cartItems: [] as CartItem[],

    // 订单相关
    orders: [] as Order[],

    // 收藏相关
    favorites: [] as FavoriteItem[],

    // 地址相关
    addresses: [] as Address[],

    // 全局状态
    loading: {
      global: false
    } as LoadingState,

    // 错误信息
    error: null as ErrorInfo | null
  }),

  getters: {
    // 用户相关
    isLoggedIn: (state) => !!state.token && !!state.user,

    // 购物车相关
    cartItemsCount: (state) => {
      return state.cartItems.reduce((total, item) => total + item.cartCount, 0)
    },

    cartTotalPrice: (state) => {
      return state.cartItems.reduce((total, item) => total + (item.price * item.cartCount), 0)
    },

    checkedCartItems: (state) => {
      return state.cartItems.filter(item => item.checked !== false)
    },

    checkedCartTotalPrice: (state) => {
      return state.cartItems
        .filter(item => item.checked !== false)
        .reduce((total, item) => total + (item.price * item.cartCount), 0)
    },

    // 订单相关
    ordersByStatus: (state) => {
      return (status: string) => state.orders.filter(order => order.status === status)
    },

    // 收藏相关
    favoritesCount: (state) => state.favorites.length,

    isFavorite: (state) => {
      return (productId: number) => state.favorites.some(item => item.productId === productId)
    },

    // 地址相关
    defaultAddress: (state) => {
      return state.addresses.find(addr => addr.isDefault) || state.addresses[0] || null
    }
  },

  actions: {
    // ==================== 用户相关 ====================

    /**
     * 设置用户信息
     */
    setUser(user: User | null) {
      this.user = user
      this.saveToStorage()
    },

    /**
     * 设置token
     */
    setToken(token: string) {
      this.token = token
      this.saveToStorage()
    },

    /**
     * 登出
     */
    logout() {
      this.user = null
      this.token = ''
      this.cartItems = []
      this.orders = []
      this.favorites = []
      this.addresses = []
      this.clearStorage()
    },

    // ==================== 购物车相关 ====================

    /**
     * 添加商品到购物车
     */
    addToCart(product: any) {
      const existingItem = this.cartItems.find(item => item.productId === product.id)

      if (existingItem) {
        existingItem.cartCount += 1
      } else {
        const cartItem: CartItem = {
          id: Date.now(), // 临时ID
          productId: product.id,
          title: product.details || product.title,
          img: product.img,
          price: product.price || 99.9, // 默认价格
          cartCount: 1,
          checked: true
        }
        this.cartItems.push(cartItem)
      }
      this.saveToStorage()
    },

    /**
     * 更新购物车商品数量
     */
    updateCartItemCount(productId: number, count: number) {
      const item = this.cartItems.find(item => item.productId === productId)
      if (item) {
        if (count <= 0) {
          this.removeFromCart(productId)
        } else {
          item.cartCount = count
          this.saveToStorage()
        }
      }
    },

    /**
     * 从购物车移除商品
     */
    removeFromCart(productId: number) {
      const index = this.cartItems.findIndex(item => item.productId === productId)
      if (index > -1) {
        this.cartItems.splice(index, 1)
        this.saveToStorage()
      }
    },

    /**
     * 清空购物车
     */
    clearCart() {
      this.cartItems = []
      this.saveToStorage()
    },

    /**
     * 切换商品选中状态
     */
    toggleCartItemCheck(productId: number) {
      const item = this.cartItems.find(item => item.productId === productId)
      if (item) {
        item.checked = !item.checked
        this.saveToStorage()
      }
    },

    /**
     * 全选/取消全选
     */
    toggleAllCartItems(checked: boolean) {
      this.cartItems.forEach(item => {
        item.checked = checked
      })
      this.saveToStorage()
    },

    // ==================== 收藏相关 ====================

    /**
     * 添加收藏
     */
    addToFavorites(product: any) {
      const exists = this.favorites.some(item => item.productId === product.id)
      if (!exists) {
        const favoriteItem: FavoriteItem = {
          id: Date.now(),
          userId: this.user?.id || 0,
          productId: product.id,
          product: product,
          createTime: new Date().toISOString()
        }
        this.favorites.push(favoriteItem)
        this.saveToStorage()
      }
    },

    /**
     * 取消收藏
     */
    removeFromFavorites(productId: number) {
      const index = this.favorites.findIndex(item => item.productId === productId)
      if (index > -1) {
        this.favorites.splice(index, 1)
        this.saveToStorage()
      }
    },

    // ==================== 地址相关 ====================

    /**
     * 添加地址
     */
    addAddress(address: Omit<Address, 'id' | 'userId' | 'createTime' | 'updateTime'>) {
      const newAddress: Address = {
        ...address,
        id: Date.now(),
        userId: this.user?.id || 0,
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString()
      }

      // 如果设为默认地址，取消其他地址的默认状态
      if (newAddress.isDefault) {
        this.addresses.forEach(addr => addr.isDefault = false)
      }

      this.addresses.push(newAddress)
      this.saveToStorage()
    },

    /**
     * 更新地址
     */
    updateAddress(addressData: Partial<Address> & { id: number }) {
      const index = this.addresses.findIndex(addr => addr.id === addressData.id)
      if (index > -1) {
        // 如果设为默认地址，取消其他地址的默认状态
        if (addressData.isDefault) {
          this.addresses.forEach(addr => addr.isDefault = false)
        }

        this.addresses[index] = {
          ...this.addresses[index],
          ...addressData,
          updateTime: new Date().toISOString()
        }
        this.saveToStorage()
      }
    },

    /**
     * 删除地址
     */
    removeAddress(addressId: number) {
      const index = this.addresses.findIndex(addr => addr.id === addressId)
      if (index > -1) {
        this.addresses.splice(index, 1)
        this.saveToStorage()
      }
    },

    // ==================== 订单相关 ====================

    /**
     * 添加订单
     */
    addOrder(order: Omit<Order, 'id' | 'createTime' | 'updateTime'>) {
      const newOrder: Order = {
        ...order,
        id: Date.now(),
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString()
      }
      this.orders.unshift(newOrder) // 新订单放在前面
      this.saveToStorage()
    },

    // ==================== 数据持久化 ====================

    /**
     * 保存到本地存储
     */
    saveToStorage() {
      try {
        const data = {
          user: this.user,
          token: this.token,
          cartItems: this.cartItems,
          orders: this.orders,
          favorites: this.favorites,
          addresses: this.addresses
        }
        localStorage.setItem('app-store', JSON.stringify(data))
      } catch (error) {
        console.error('保存数据到本地存储失败:', error)
      }
    },

    /**
     * 从本地存储加载
     */
    loadFromStorage() {
      try {
        const data = localStorage.getItem('app-store')
        if (data) {
          const parsed = JSON.parse(data)
          this.user = parsed.user || null
          this.token = parsed.token || ''
          this.cartItems = parsed.cartItems || []
          this.orders = parsed.orders || []
          this.favorites = parsed.favorites || []
          this.addresses = parsed.addresses || []
        }
      } catch (error) {
        console.error('从本地存储加载数据失败:', error)
      }
    },

    /**
     * 清除本地存储
     */
    clearStorage() {
      try {
        localStorage.removeItem('app-store')
      } catch (error) {
        console.error('清除本地存储失败:', error)
      }
    },

    // ==================== 兼容性方法 ====================

    /**
     * 兼容旧版本的tocart方法
     */
    tocart(product: any) {
      this.addToCart(product)
    }
  }
})