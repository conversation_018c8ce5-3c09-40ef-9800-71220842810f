/**
 * 项目通用类型定义
 */

// ==================== 基础类型 ====================

/** API响应基础结构 */
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

/** 分页参数 */
export interface PaginationParams {
  page: number;
  pageSize: number;
}

/** 分页响应数据 */
export interface PaginationResponse<T> {
  list: T[];
  total: number;
  page: number;
  pageSize: number;
}

// ==================== 用户相关 ====================

/** 用户信息 */
export interface User {
  id: number;
  name: string;
  email?: string;
  phone?: string;
  avatar?: string;
  gender?: 'male' | 'female' | 'unknown';
  birthday?: string;
  createTime: string;
  updateTime: string;
}

/** 登录表单 */
export interface LoginForm {
  name: string;
  password: string;
}

/** 登录响应 */
export interface LoginResponse {
  token: string;
  user: User;
}

// ==================== 商品相关 ====================

/** 商品信息 */
export interface Product {
  id: number;
  title: string;
  details: string;
  img: string;
  images?: string[]; // 商品图片列表
  price: number;
  originalPrice?: number; // 原价
  stock: number; // 库存
  sales?: number; // 销量
  category: string;
  categoryId: number;
  tags?: string[];
  description?: string; // 详细描述
  specifications?: ProductSpecification[]; // 规格
  reviews?: ProductReview[]; // 评价
  rating?: number; // 评分
  createTime: string;
  updateTime: string;
}

/** 商品规格 */
export interface ProductSpecification {
  id: number;
  name: string; // 规格名称，如"颜色"、"尺寸"
  options: SpecificationOption[]; // 规格选项
}

/** 规格选项 */
export interface SpecificationOption {
  id: number;
  value: string; // 选项值，如"红色"、"L"
  price?: number; // 该选项的价格差异
  stock?: number; // 该选项的库存
}

/** 商品评价 */
export interface ProductReview {
  id: number;
  userId: number;
  userName: string;
  userAvatar?: string;
  rating: number; // 1-5星
  content: string;
  images?: string[];
  createTime: string;
}

/** 商品分类 */
export interface Category {
  id: number;
  name: string;
  icon?: string;
  parentId?: number;
  children?: Category[];
}

// ==================== 购物车相关 ====================

/** 购物车商品 */
export interface CartItem {
  id: number;
  productId: number;
  title: string;
  img: string;
  price: number;
  cartCount: number;
  specifications?: SelectedSpecification[]; // 选中的规格
  checked?: boolean; // 是否选中
}

/** 选中的规格 */
export interface SelectedSpecification {
  specId: number;
  specName: string;
  optionId: number;
  optionValue: string;
}

// ==================== 订单相关 ====================

/** 订单状态 */
export type OrderStatus = 
  | 'pending'    // 待付款
  | 'paid'       // 已付款
  | 'shipped'    // 已发货
  | 'delivered'  // 已送达
  | 'completed'  // 已完成
  | 'cancelled'  // 已取消
  | 'refunded';  // 已退款

/** 订单信息 */
export interface Order {
  id: number;
  orderNumber: string;
  userId: number;
  status: OrderStatus;
  items: OrderItem[];
  totalAmount: number;
  shippingFee: number;
  finalAmount: number;
  shippingAddress: Address;
  paymentMethod: PaymentMethod;
  paymentTime?: string;
  shippingTime?: string;
  deliveryTime?: string;
  completedTime?: string;
  remark?: string;
  createTime: string;
  updateTime: string;
}

/** 订单商品 */
export interface OrderItem {
  id: number;
  productId: number;
  title: string;
  img: string;
  price: number;
  quantity: number;
  specifications?: SelectedSpecification[];
  subtotal: number;
}

// ==================== 地址相关 ====================

/** 收货地址 */
export interface Address {
  id: number;
  userId: number;
  name: string; // 收货人姓名
  phone: string;
  province: string;
  city: string;
  district: string;
  detail: string; // 详细地址
  postalCode?: string;
  isDefault: boolean;
  createTime: string;
  updateTime: string;
}

// ==================== 支付相关 ====================

/** 支付方式 */
export type PaymentMethod = 'wechat' | 'alipay' | 'balance' | 'bank';

/** 支付信息 */
export interface PaymentInfo {
  method: PaymentMethod;
  amount: number;
  orderNumber: string;
  timestamp: string;
}

// ==================== 收藏相关 ====================

/** 收藏商品 */
export interface FavoriteItem {
  id: number;
  userId: number;
  productId: number;
  product: Product;
  createTime: string;
}

// ==================== 搜索相关 ====================

/** 搜索参数 */
export interface SearchParams {
  keyword?: string;
  categoryId?: number;
  minPrice?: number;
  maxPrice?: number;
  sortBy?: 'default' | 'price_asc' | 'price_desc' | 'sales' | 'rating';
  page?: number;
  pageSize?: number;
}

/** 搜索结果 */
export interface SearchResult {
  products: Product[];
  total: number;
  page: number;
  pageSize: number;
  filters: SearchFilter[];
}

/** 搜索筛选器 */
export interface SearchFilter {
  type: 'category' | 'price' | 'brand';
  name: string;
  options: FilterOption[];
}

/** 筛选选项 */
export interface FilterOption {
  value: string | number;
  label: string;
  count: number;
}

// ==================== 应用状态相关 ====================

/** 全局加载状态 */
export interface LoadingState {
  global: boolean;
  [key: string]: boolean;
}

/** 错误信息 */
export interface ErrorInfo {
  code?: number;
  message: string;
  timestamp: string;
}

/** 网络状态 */
export interface NetworkState {
  online: boolean;
  effectiveType?: string;
}

// ==================== 组件Props相关 ====================

/** 商品卡片Props */
export interface ProductCardProps {
  product: Product;
  showAddToCart?: boolean;
  showFavorite?: boolean;
}

/** 地址选择器Props */
export interface AddressSelectorProps {
  modelValue?: Address;
  addresses: Address[];
}

// ==================== 工具类型 ====================

/** 使某些属性可选 */
export type PartialBy<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

/** 使某些属性必需 */
export type RequiredBy<T, K extends keyof T> = Omit<T, K> & Required<Pick<T, K>>;

/** 创建表单类型（去除id、时间等字段） */
export type CreateForm<T> = Omit<T, 'id' | 'createTime' | 'updateTime'>;

/** 更新表单类型（id必需，其他可选） */
export type UpdateForm<T> = RequiredBy<Partial<T>, 'id'>;
