{"name": "vue-xdkt", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build"}, "dependencies": {"@tailwindcss/postcss": "^4.1.11", "@tailwindcss/vite": "^4.1.11", "axios": "^1.10.0", "element-plus": "^2.10.2", "pinia": "^3.0.3", "router": "^2.2.0", "tailwindcss": "^4.1.11", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@tsconfig/node22": "^22.0.2", "@types/mockjs": "^1.0.10", "@types/node": "^22.15.32", "@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "mockjs": "^1.1.0", "npm-run-all2": "^8.0.4", "typescript": "~5.8.0", "vite": "^7.0.0", "vite-plugin-vue-devtools": "^7.7.7", "vue-tsc": "^2.2.10"}}