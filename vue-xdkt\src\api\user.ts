import axios from 'axios'

// 登录参数类型
export interface LoginParams {
  username: string
  password: string
}

// 登录响应类型
export interface LoginResponse {
  code: number
  message: string
  data?: {
    token: string
    userId?: string
    username?: string
  }
}

// 用户登录
export const userLogin = (data: LoginParams): Promise<LoginResponse> => {
  return axios.post('/api/user/login', data).then(res => res.data)
}

// 用户注册
export interface RegisterParams {
  username: string
  password: string
  confirmPassword?: string
  email?: string
}

export interface RegisterResponse {
  code: number
  message: string
  data?: {
    userId: string
    username: string
  }
}

export const userRegister = (data: RegisterParams): Promise<RegisterResponse> => {
  return axios.post('/api/user/register', data).then(res => res.data)
}

// 获取用户信息
export interface UserInfoResponse {
  code: number
  message: string
  data?: {
    userId: string
    username: string
    avatar: string
    role: string
    email: string
    createTime: string
    lastLoginTime: string
  }
}

export const getUserInfo = (): Promise<UserInfoResponse> => {
  return axios.get('/api/user/info').then(res => res.data)
}
