<script setup>
import { ref, reactive } from "vue";
import { useUserStore } from "@/store/user";
import { useModalStore } from "@/store/modal";
import { ElMessage } from "element-plus";
import { userRegister } from "@/api/user";

const registerForm = reactive({
  username: "",
  password: "",
  confirmPassword: ""
});

const user = useUserStore();
const modal = useModalStore();

// 注册提交按钮
async function handleFinish() {
  // 密码验证;
  if (registerForm.password !== registerForm.confirmPassword) {
    ElMessage({
      message: "两次密码不一致!",
      type: "warning",
    });
    return;
  }
  
  try {
    const res = await userRegister({
      username: registerForm.username,
      password: registerForm.password
    });
    
    if (res.code === 0) { // Mock API返回code为0表示成功
      // 关闭注册弹窗
      modal.closeModals();
      ElMessage({
        message: "注册成功!",
        type: "success",
      });
      // 打开登录弹窗
      modal.openLoginModal();
    } else {
      ElMessage({
        message: res.message || "注册失败",
        type: "error",
      });
    }
  } catch (error) {
    console.error("注册失败:", error);
    ElMessage({
      message: "注册失败，请稍后再试",
      type: "error",
    });
  }
}
</script>
<template>
  <el-dialog width="400px" v-model="modal.registerModalVisible" class="relative">
    <h1 class="text-center c-#404040 text-22px font-normal mb-1.5">账号注册</h1>
    <div class="pb-44px flex items-center justify-center w-full">
      <el-form name="register" :model="registerForm">
        <!-- 账号 -->
        <el-form-item prop="username">
          <el-input placeholder="请输入账号" v-model="registerForm.username" />
        </el-form-item>
        <!-- 密码 -->
        <el-form-item prop="password">
          <el-input
            type="password"
            placeholder="请输入密码"
            v-model="registerForm.password"
          />
        </el-form-item>
        <!-- 确认密码 -->
        <el-form-item prop="confirmPassword">
          <el-input
            type="password"
            placeholder="请再次输入密码"
            v-model="registerForm.confirmPassword"
          />
        </el-form-item>
        <!-- 注册 -->
        <el-form-item>
          <el-button
            type="primary"
            @click="handleFinish"
            class="flex w-full items-center justify-center bg-#444b52 text-white rounded-full"
          >
            <span>立即注册</span>
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 跳转登录 -->
    <div
      class="absolute w-full h-44px bottom-0 left-0 bg-[rgba(77,85,93,0.1)] flex items-center justify-center"
    >
      <span>已有账号?</span>
      <span
        class="text-blue-400 cursor-pointer"
        @click="modal.openLoginModal()"
      >
        立即登录
      </span>
    </div>
  </el-dialog>
</template>
