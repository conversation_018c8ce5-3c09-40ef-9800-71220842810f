import { createApp } from 'vue'
import App from './App.vue'
import './assets/mian.css'
import router from './router'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import { createPinia } from 'pinia'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// 导入mock数据，开发环境下使用
import './mock'

const app = createApp(App)
const pinia = createPinia()

Object.keys(ElementPlusIconsVue).forEach((key) => {
    app.component(key, ElementPlusIconsVue[key as keyof typeof ElementPlusIconsVue]);
  });
  

app.use(router).use(pinia).use(ElementPlus).mount('#app')
