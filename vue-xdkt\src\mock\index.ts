import Mock from 'mockjs'

// 配置拦截HTTP请求的响应时间，单位是毫秒
Mock.setup({
  timeout: '200-600'
})

// 用户相关的mock数据
const userList = {
  // 用户登录
  'POST /api/user/login': {
    code: 0,
    data: {
      'token': 'mockToken123456789',
      'userId': '@id',
      'username': '@cname',
      'avatar': '@image("200x200","#4A7BF7","头像")',
      'role': 'user'
    },
    msg: '登录成功'
  },
  
  // 用户注册
  'POST /api/user/register': {
    code: 0,
    data: {
      'userId': '@id',
      'username': '@cname'
    },
    msg: '注册成功'
  },
  
  // 获取用户信息
  'GET /api/user/info': {
    code: 0,
    data: {
      'userId': '@id',
      'username': '@cname',
      'avatar': '@image("200x200","#4A7BF7","头像")',
      'role': 'user',
      'email': '@email',
      'createTime': '@datetime',
      'lastLoginTime': '@datetime'
    },
    msg: '获取用户信息成功'
  }
}

// 课程相关的mock数据
const courseList = {
  // 获取课程列表
  'GET /api/courses': {
    code: 0,
    data: {
      'total|10-100': 100,
      'list|10': [{
        'id|+1': 1,
        'title': '@ctitle(5, 20)',
        'cover': '@image("300x200","#4A7BF7","小滴课堂")',
        'price|1-1000': 100,
        'oldPrice|1-2000': 200,
        'teacherName': '@cname',
        'teacherAvatar': '@image("100x100","#4A7BF7","讲师")',
        'level|1': ['入门', '初级', '中级', '高级', '资深'],
        'category|1': ['前端', '后端', '大数据', '人工智能', '运维', '测试', '其他'],
        'studyCount|100-10000': 1000,
        'chapterCount|5-50': 10,
        'createTime': '@datetime'
      }]
    },
    msg: '获取课程列表成功'
  },
  
  // 获取课程详情
  'GET /api/course/:id': {
    code: 0,
    data: {
      'id': '@natural(1, 100)',
      'title': '@ctitle(5, 20)',
      'cover': '@image("800x400","#4A7BF7","小滴课堂")',
      'price|1-1000': 100,
      'oldPrice|1-2000': 200,
      'teacherName': '@cname',
      'teacherAvatar': '@image("100x100","#4A7BF7","讲师")',
      'teacherInfo': '@cparagraph(1, 3)',
      'level|1': ['入门', '初级', '中级', '高级', '资深'],
      'category|1': ['前端', '后端', '大数据', '人工智能', '运维', '测试', '其他'],
      'studyCount|100-10000': 1000,
      'score|1-50': 45,
      'description': '@cparagraph(3, 7)',
      'chapterCount|5-50': 10,
      'chapters|5-10': [{
        'id|+1': 1,
        'title': '@ctitle(5, 20)',
        'lessons|3-8': [{
          'id|+1': 1,
          'title': '@ctitle(5, 30)',
          'duration': '@time("mm:ss")',
          'isFree|1-5': true
        }]
      }],
      'createTime': '@datetime'
    },
    msg: '获取课程详情成功'
  }
}

// 将所有mock数据整合并注册
const mocks: Record<string, any> = {
  ...userList,
  ...courseList
}

// 注册所有模拟数据接口
Object.keys(mocks).forEach(key => {
  const [method, url] = key.split(' ')
  const mockUrl = url.includes(':') 
    ? new RegExp(url.replace(/:([^/]+)/g, '([^/]+)')) 
    : url
    
  Mock.mock(mockUrl, method.toLowerCase(), mocks[key])
})

export default Mock