import { defineStore } from "pinia";

export const useModalStore = defineStore("modal", {
  state: () => ({
    loginModalVisible: false,
    registerModalVisible: false,
  }),

  actions: {
    openLoginModal() {
      this.loginModalVisible = true;
      this.registerModalVisible = false;
    },

    openRegisterModal() {
      this.registerModalVisible = true;
      this.loginModalVisible = false;
    },

    closeModals() {
      this.loginModalVisible = false;
      this.registerModalVisible = false;
    }
  },
});
