import { defineStore } from "pinia";
import { ElMessage } from "element-plus";
import { userLogin } from "@/api/user";
import type { LoginParams, LoginResponse } from "@/api/user";

export const useUserStore = defineStore("user", {
  state: () => ({
    token: localStorage.getItem('token') || '',
    userInfo: {} as Record<string, any>,
  }),
  
  // 计算属性
  getters: {
    isLoggedIn: (state) => !!state.token,
  },
  
  // 方法
  actions: {
    // 登录方法
    async login(loginForm: LoginParams) {
     try {  
      if (!loginForm.username.trim()) {
        console.error('请输入用户名');
        return false;
      }
      if (!loginForm.password.trim()) {
        console.error('请输入密码');
        return false;
      }
      
        const res: LoginResponse = await userLogin(loginForm)
        
        if (res.code === 0) { // Mock API返回code为0表示成功
          // 存储token到状态和本地存储
          this.token = res.data?.token || ''
          localStorage.setItem('token', this.token)
          
          // 提示登录成功
          ElMessage({
            message: '登录成功',
            type: 'success'
          })
          
          return true
        } else {
          ElMessage({
            message: res.message || '登录失败',
            type: 'error'
          })
          return false
        }
      } catch (error) {
        console.log('登录出错:', error)
        ElMessage({
          message: '登录失败，请稍后再试',
          type: 'error'
        })
        return false
      }
    },

    // 登出方法
    logout() {
      this.token = ''
      this.userInfo = {}
      localStorage.removeItem('token')
    },
  },
});
