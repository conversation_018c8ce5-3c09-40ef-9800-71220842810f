import axios from 'axios'
import type { AxiosRequestConfig } from 'axios'
//创建axios实例
const service = axios.create({
    baseURL:'/api',
    timeout: 1000,
    headers:{
        'Content-Type': 'application/json'
    }
})

// 请求拦截器
service.interceptors.request.use(
    config=>{
        const token = localStorage.getItem('token')
        if(token){
            config.headers.Authorization = `Bearer ${token}`
        }
        return config
    },
    error=>{
        console.log(error)
        return Promise.reject(error)
    }
)
service.interceptors.response.use(
    response=>{
        const res = response.data;
        if(res.code !== 200){
            console.log(res.message)
            return Promise.reject(new Error(res.message))
        }
        return res.data
},
    error=>{
        const status = error.response?.status
        switch(status){
            case 401:
                console.log('登录过期')
                break
            case 500:
                console.log('服务器错误')
                break
            default:
                console.log('其他错误')
                break;
        }
        return Promise.reject(error)
    }
)

// // 封装一个泛型请求方法
const request = <T = any>(config: AxiosRequestConfig): Promise<T> => {
    return service(config) as unknown as Promise<T>
}

export default request